// 整合所有消息处理到一个监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // 处理GET_TAB_ID消息
  if (request.type === 'GET_TAB_ID') {
    sendResponse({ tabId: sender.tab.id });
    return true;
  }
  
  // 处理MOVE_MOUSE消息
  if (request.type === 'MOVE_MOUSE') {
    const { x, y, tabId } = request;
    
    // 连接 debugger
    chrome.debugger.attach({ tabId }, "1.3")
      .then(() => {
        // 移动鼠标
        return chrome.debugger.sendCommand({ tabId }, "Input.dispatchMouseEvent", {
          type: "mouseMoved",
          x: x,
          y: y
        });
      })
      .then(() => {
        // 等待一小段时间
        return new Promise(resolve => setTimeout(resolve, 500));
      })
      .then(() => {
        // 点击鼠标
        return chrome.debugger.sendCommand({ tabId }, "Input.dispatchMouseEvent", {
          type: "mousePressed",
          button: "left",
          x: x,
          y: y
        });
      })
      .then(() => {
        return chrome.debugger.sendCommand({ tabId }, "Input.dispatchMouseEvent", {
          type: "mouseReleased",
          button: "left",
          x: x,
          y: y
        });
      })
      .then(() => {
        // 断开 debugger
        return chrome.debugger.detach({ tabId });
      })
      .then(() => {
        sendResponse({ success: true });
      })
      .catch((error) => {
        console.error('Debugger error:', error);
        sendResponse({ success: false, error: error.message });
      });
    
    return true; // 保持消息通道开放
  }

  if (request.action === 'ocrRequest') {
    // 转发OCR请求到第三方服务
    fetch('https://api.yourocrservice.com/recognize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${request.apiKey}`
      },
      body: JSON.stringify({
        image: request.imageData
      })
    })
    .then(response => response.json())
    .then(data => {
      sendResponse({success: true, text: data.text});
    })
    .catch(error => {
      console.error('OCR请求失败:', error);
      sendResponse({success: false, error: error.message});
    });
    
    return true; // 保持消息通道开放
  }

  if (request.action === 'captureCanvas') {
    chrome.scripting.executeScript({
      target: {tabId: request.tabId},
      func: () => {
        // 这段代码在页面上下文中执行，可以避免扩展的限制
        try {
          const iframe = document.querySelector('iframe[src*="/web/frame/c-resume/"]');
          if (!iframe || !iframe.contentDocument) return null;
          
          const canvas = iframe.contentDocument.querySelector('canvas');
          return canvas ? canvas.toDataURL('image/png') : null;
        } catch (e) {
          console.error('页面脚本获取canvas失败:', e);
          return null;
        }
      }
    }).then(results => {
      const dataUrl = results[0].result;
      sendResponse({success: !!dataUrl, dataUrl});
    }).catch(error => {
      console.error('执行脚本失败:', error);
      sendResponse({success: false, error: error.message});
    });
    
    return true; // 保持消息通道开放
  }

  if (request.action === 'captureVisibleTab') {
    try {
      chrome.tabs.captureVisibleTab(null, {format: 'png'}, (dataUrl) => {
        if (chrome.runtime.lastError) {
          console.error('截图失败:', chrome.runtime.lastError.message);
          sendResponse({success: false, error: chrome.runtime.lastError.message});
        } else {
          console.log('截图成功，数据长度:', dataUrl.length);
          sendResponse({success: true, imageData: dataUrl});
        }
      });
      return true; // 保持消息通道开放，等待异步响应
    } catch (e) {
      console.error('调用截图API时出错:', e);
      sendResponse({success: false, error: e.message});
    }
  }

  if (request.action === 'injectWasmFix') {
    chrome.scripting.executeScript({
      target: {tabId: request.tabId},
      func: () => {
        try {
          // 找到iframe
          const iframe = document.querySelector('iframe[src*="/web/frame/c-resume/"]');
          if (!iframe || !iframe.contentWindow) return false;
          
          // 注入修复代码
          const script = document.createElement('script');
          script.textContent = `
            // 覆盖WebAssembly.instantiateStreaming方法，使用兼容方式
            const originalInstantiateStreaming = WebAssembly.instantiateStreaming;
            WebAssembly.instantiateStreaming = async function(response, importObject) {
              try {
                return await originalInstantiateStreaming(response, importObject);
              } catch (e) {
                console.log('WebAssembly.instantiateStreaming失败，尝试替代方案');
                const buffer = await response.arrayBuffer();
                return WebAssembly.instantiate(buffer, importObject);
              }
            };
          `;
          
          // 将脚本注入到iframe内
          iframe.contentDocument.head.appendChild(script);
          return true;
        } catch (e) {
          console.error('注入WASM修复失败:', e);
          return false;
        }
      }
    }).then(results => {
      sendResponse({success: results[0]?.result || false});
    }).catch(error => {
      sendResponse({success: false, error: error.message});
    });
    
    return true;
  }

  if (request.action === 'getCanvasSafely') {
    chrome.scripting.executeScript({
      target: {tabId: request.tabId},
      func: () => {
        try {
          // 这段代码在页面上下文中执行，可以避免扩展的跨域限制
          const iframe = document.querySelector('iframe[src*="/web/frame/c-resume/"]');
          if (!iframe || !iframe.contentDocument) return null;
          
          // 创建一个临时脚本来提取Canvas数据
          const script = document.createElement('script');
          script.textContent = `
            try {
              const canvas = document.querySelector('canvas');
              if (!canvas) {
                window.canvasExtractResult = { success: false, error: 'Canvas不存在' };
              } else {
                try {
                  const dataURL = canvas.toDataURL('image/png');
                  window.canvasExtractResult = { success: true, dataURL };
                } catch (e) {
                  window.canvasExtractResult = { success: false, error: e.message };
                }
              }
            } catch (e) {
              window.canvasExtractResult = { success: false, error: e.message };
            }
          `;
          
          // 在iframe内执行脚本
          iframe.contentDocument.head.appendChild(script);
          
          // 给脚本执行的时间
          setTimeout(() => {
            const result = iframe.contentWindow.canvasExtractResult;
            return result;
          }, 500);
        } catch (e) {
          return { success: false, error: e.message };
        }
      }
    }).then(results => {
      sendResponse(results[0].result || { success: false });
    }).catch(error => {
      sendResponse({ success: false, error: error.message });
    });
    
    return true;
  }

  if (request.type === 'GET_TAB_ID') {
    sendResponse({ tabId: sender.tab.id });
    return true;
  }

  if (request.action === 'injectOCR') {
    chrome.scripting.executeScript({
      target: {tabId: request.tabId},
      files: ['tesseract.min.js'] // 提前打包Tesseract.js库
    }).then(() => {
      return chrome.scripting.executeScript({
        target: {tabId: request.tabId},
        func: () => {
          // 在页面上下文中执行OCR
          const iframe = document.querySelector('iframe[src*="/web/frame/c-resume/"]');
          if (!iframe || !iframe.contentDocument) return { success: false, error: '找不到iframe' };
          
          const canvas = iframe.contentDocument.querySelector('canvas');
          if (!canvas) return { success: false, error: '找不到canvas' };
          
          // 使用Tesseract.js直接识别
          return new Promise(resolve => {
            Tesseract.recognize(canvas, 'chi_sim').then(result => {
              resolve({ success: true, text: result.text });
            }).catch(err => {
              resolve({ success: false, error: err.message });
            });
          });
        }
      });
    }).then(results => {
      sendResponse(results[0].result);
    }).catch(error => {
      sendResponse({ success: false, error: error.message });
    });
    
    return true;
  }
});
