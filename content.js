// API配置
window.API_CONFIG = {
  development: {
    baseUrl: 'http://localhost:8787',
  },
  production: {
    baseUrl: 'http://127.0.0.1:8081',
  }
};

// 不再需要导入语句，因为所有变量和函数都是全局的

// UI和初始化相关的代码
console.log('content.js 已加载');

// 全局状态变量
let isAnalyzing = false;
let isScrolling = false;
let lastScrollPosition = 0;
let scrollStuckCount = 0;
const MAX_SCROLL_STUCK = 3;

// 添加全局锁
let analysisLock = false;

// 工作中文案
const workingMessages = [
  'AI正在打工中...',
  'AI正在筛选简历...',
  'AI正在加班加点中...',
  'AI正在冲刺...',
  'AI火力全开中...'
];

// 初始化tabId
let tabId = null;

// 获取当前标签页ID并添加错误处理
chrome.runtime.sendMessage({ type: 'GET_TAB_ID' }, (response) => {
  if (chrome.runtime.lastError) {
    console.error('获取tabId时发生错误:', chrome.runtime.lastError);
    return;
  }
  
  if (response && response.tabId) {
    tabId = response.tabId;
    window.tabId = tabId;
    console.log('成功获取tabId:', tabId);
  } else {
    console.error('获取tabId失败: 响应无效', response);
  }
});

// 导出获取tabId的方法
window.getTabId = () => tabId;

// 添加初始化标志和结果持久化标志
let isInitialized = false;
let isResultsPersisted = false;
let isObserverSetup = false;

// 添加结果缓存
let lastResults = null;
let lastScoreThreshold = null;

// 定义一个全局唯一ID，用于标识容器
const CONTAINER_ID = 'ai-resume-container-unique';
let observer = null;
let initializationTimeout = null;

// 添加闪动效果的CSS
const flashStyle = `
@keyframes highlight-border {
  0% { border: 2px solid #1890ff; box-shadow: 0 0 8px #1890ff; }
  50% { border: 2px solid transparent; box-shadow: none; }
  100% { border: 2px solid #1890ff; box-shadow: 0 0 8px #1890ff; }
}

.highlight-element {
  animation: highlight-border 1s ease-in-out 3;
}`;

// 添加样式到页面
const styleElement = document.createElement('style');
styleElement.textContent = flashStyle;
document.head.appendChild(styleElement);

// 简化的初始化函数
async function init() {
  console.log('初始化开始，当前路径:', window.location.pathname);
  
  // 检查是否是目标页面
  const isValidPage = window.location.pathname.includes('/web/chat/search') || 
                     window.location.pathname.includes('/web/chat/index') ||
                     window.location.pathname.includes('/web/chat/recommend');
  
  if (!isValidPage) {
    console.log('不是目标页面，跳过初始化');
    return;
  }

  try {
    // 如果已经存在容器，直接返回
    const existingContainer = document.getElementById(CONTAINER_ID);
    if (existingContainer) {
      console.log('容器已存在，跳过创建');
      return existingContainer;
    }

    // 移除所有旧的AI助手容器（基于类名）
    const oldContainers = document.querySelectorAll('.ai-resume-container:not(#' + CONTAINER_ID + ')');
    oldContainers.forEach(container => {
      console.log('移除旧的AI助手容器');
      container.remove();
    });

    // 检查是否已加载样式表
    if (!document.querySelector('link[href*="styles.css"]')) {
      console.log('添加样式表');
      const styleLink = document.createElement('link');
      styleLink.rel = 'stylesheet';
      styleLink.href = chrome.runtime.getURL('styles.css');
      document.head.appendChild(styleLink);
    }

    // 加载用户配置
    await new Promise((resolve) => {
      chrome.storage.sync.get(['apiKey', 'scoreThreshold', 'prompt', 'apiEnvironment'], (result) => {
        window.resumeManager.currentConfig = {
          apiKey: result.apiKey || '',
          scoreThreshold: result.scoreThreshold || 80,
          apiEnvironment: result.apiEnvironment || 'production'
        };
        resolve();
      });
    });
    console.log('配置已加载到resumeManager');

    // 创建UI
    console.log('开始添加AI按钮');
    const container = addAIButton();
    container.id = CONTAINER_ID;  // 添加唯一ID
    
    // 加载分析结果
    console.log('开始加载分析结果');
    await window.resumeManager.loadAnalysisResults();
    
    // 显示结果
    console.log('更新结果显示');
    await updateResultsDisplay();
    
    console.log('开始添加键盘快捷键');
    addKeyboardShortcuts();

    // 设置事件监听
    setupEventListeners(container);

    console.log('初始化完成');
    return container;
  } catch (error) {
    console.error('初始化过程出错:', error);
  }
}

// 简化的观察器逻辑
function setupObserver() {
  // 如果已存在观察器，先断开连接
  if (observer) {
    observer.disconnect();
    observer = null;
  }

  // 创建新的观察器
  observer = new MutationObserver(() => {
    // 清除之前的超时
    if (initializationTimeout) {
      clearTimeout(initializationTimeout);
    }

    // 设置新的超时，延迟处理DOM变化
    initializationTimeout = setTimeout(async () => {
      const container = document.getElementById(CONTAINER_ID);
      
      if (!container) {
        // 容器不存在，重新初始化
        console.log('检测到AI助手容器不存在，重新初始化');
        await init();
      } else {
        // 容器存在，检查结果列表
        const resultsList = container.querySelector('.results-list');
        
        if (!resultsList || resultsList.children.length === 0) {
          // 结果列表为空，重新显示结果
          console.debug('检测到结果列表为空，重新显示结果');
          await updateResultsDisplay();
        }
      }
    }, 1000);
  });

  // 观察body的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('观察器设置完成');
}

// 简化页面加载逻辑
if (document.readyState === 'loading') {
  console.log('页面正在加载，等待 DOMContentLoaded');
  document.addEventListener('DOMContentLoaded', () => {
    console.log('DOMContentLoaded 触发');
    init().then(() => setupObserver());
  });
} else {
  console.log('页面已加载完成，直接初始化');
  init().then(() => setupObserver());
}

// 修改添加按钮函数
function addAIButton() {
  // 如果已经存在，直接返回
  const existingContainer = document.getElementById(CONTAINER_ID);
  if (existingContainer) {
    return existingContainer;
  }

  const iconUrl = chrome.runtime.getURL('images/icon.png');
  console.log('使用图标URL:', iconUrl);

  const container = document.createElement('div');
  container.className = 'ai-resume-container';
  container.id = CONTAINER_ID;  // 添加唯一ID
  container.innerHTML = window.templates.container(iconUrl);
  document.body.appendChild(container);
  console.log('已添加AI助手容器');

  // 添加折叠按钮事件监听
  const collapseButton = container.querySelector('.collapse-button');
  if (collapseButton && !collapseButton.hasClickListener) {
    collapseButton.hasClickListener = true;
    collapseButton.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      const isCollapsed = container.classList.toggle('collapsed');
      console.log('切换收缩状态:', isCollapsed);
    });
  }

  // 添加错误关闭按钮事件监听
  const errorCloseBtn = container.querySelector('.error-container .close-btn');
  if (errorCloseBtn) {
    errorCloseBtn.addEventListener('click', () => {
      const errorContainer = container.querySelector('.error-container');
      if (errorContainer) {
        errorContainer.classList.remove('show');
      }
    });
  }

  return container;
}

// 设置事件监听函数
function setupEventListeners(container) {
  console.log('开始设置事件监听');
  
  // 获取需要的元素
  const collapseButton = container.querySelector('.collapse-button');
  const pinButton = container.querySelector('.pin-button');
  const header = container.querySelector('.ai-resume-header');
  const aiButton = container.querySelector('.ai-resume-button');
  const downloadHtmlButton = container.querySelector('.action-button:nth-child(1)');
  const downloadExcelButton = container.querySelector('.action-button:nth-child(2)');
  const clearButton = container.querySelector('.action-button:nth-child(3)');
  const filterCheckbox = container.querySelector('.qualified-filter');
  
  // 防止重复添加事件监听器
  if (aiButton && !aiButton.hasClickListener) {
    console.log('添加AI按钮事件监听');
    aiButton.hasClickListener = true;
    
    aiButton.addEventListener('click', (e) => {
      // 阻止事件冒泡，防止其他元素的点击事件影响此按钮
      e.stopPropagation();
      
      // 获取事件源信息，用于调试
      console.log('AI按钮被点击', {
        target: e.target.tagName,
        currentTarget: e.currentTarget.tagName,
        srcElement: e.srcElement?.tagName,
        path: e.composedPath?.() || []
      });
      
      // 获取按钮的当前文本内容（去除所有HTML标签和空白）
      const buttonText = aiButton.innerText.trim();
      console.log('按钮当前文本:', buttonText);
      
      if (isAnalyzing) {
        console.log('停止分析');
        stopAnalysis();
      } else if (buttonText === 'AI读简历' || buttonText === '继续分析' || buttonText === '重新开始') {
        // 支持多种按钮文本状态，都可以开始分析
        console.log('开始分析 - 按钮文本匹配:', buttonText);
        startAIAnalysis();
      } else {
        console.log('按钮文本不匹配，不执行分析:', buttonText);
      }
    });
  }

  // 下载HTML结果按钮事件
  if (downloadHtmlButton && !downloadHtmlButton.hasClickListener) {
    console.log('添加下载HTML按钮事件监听');
    downloadHtmlButton.hasClickListener = true;
    downloadHtmlButton.addEventListener('click', () => {
      console.log('下载HTML按钮被点击');
      downloadResults();
    });
  }

  // 导出Excel按钮事件
  if (downloadExcelButton && !downloadExcelButton.hasClickListener) {
    console.log('添加导出Excel按钮事件监听');
    downloadExcelButton.hasClickListener = true;
    downloadExcelButton.addEventListener('click', () => {
      console.log('导出Excel按钮被点击');
      window.templates.exportToExcel();
    });
  }

  // 清空结果按钮事件
  if (clearButton && !clearButton.hasClickListener) {
    console.log('添加清空按钮事件监听');
    clearButton.hasClickListener = true;
    clearButton.addEventListener('click', () => {
      console.log('清空按钮被点击');
      clearResults();
    });
  }

  // 拖动相关变量
  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  // 收缩/展开功能
  if (collapseButton && !collapseButton.hasClickListener) {
    console.log('添加收缩按钮事件监听');
    collapseButton.hasClickListener = true;
    collapseButton.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止事件冒泡
      const container = document.querySelector('.ai-resume-container');
      if (container) {
        container.classList.toggle('collapsed');
        console.log('切换收缩状态:', container.classList.contains('collapsed'));
      }
    });
  }

  // 固定/取消固定功能
  if (pinButton && !pinButton.hasClickListener) {
    console.log('添加固定按钮事件监听');
    pinButton.hasClickListener = true;
    pinButton.addEventListener('click', () => {
      container.classList.toggle('pinned');
      pinButton.classList.toggle('active');
      console.log('切换固定状态');
    });
  }

  // 拖动功能
  if (header && !header.hasClickListener) {
    console.log('添加拖动事件监听');
    header.hasClickListener = true;
    header.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);
  }

  function dragStart(e) {
    if (!container.classList.contains('pinned')) {
      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;
      if (e.target === header) {
        isDragging = true;
      }
    }
  }

  function drag(e) {
    if (isDragging) {
      e.preventDefault();
      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;
      xOffset = currentX;
      yOffset = currentY;
      setTranslate(currentX, currentY, container);
    }
  }

  function dragEnd(e) {
    initialX = currentX;
    initialY = currentY;
    isDragging = false;
  }

  function setTranslate(xPos, yPos, el) {
    el.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
  }

  // 添加鼠标靠近边缘自动展开功能
  let expandTimeout;
  document.addEventListener('mousemove', (e) => {
    if (container.classList.contains('collapsed') && !container.classList.contains('pinned')) {
      const threshold = 50; // 距离边缘多少像素触发
      if (e.clientX >= window.innerWidth - threshold) {
        clearTimeout(expandTimeout);
        container.classList.remove('collapsed');
      } else {
        expandTimeout = setTimeout(() => {
          container.classList.add('collapsed');
        }, 300);
      }
    }
  });

  // 添加过滤器事件监听
  if (filterCheckbox && !filterCheckbox.hasClickListener) {
    console.log('添加过滤器事件监听');
    filterCheckbox.hasClickListener = true;
    filterCheckbox.addEventListener('change', () => {
      console.log('过滤器状态改变:', filterCheckbox.checked);
      updateResultsDisplay();
    });
  }

  console.log('事件监听设置完成');
}

// 修改更新结果显示函数
async function updateResultsDisplay() {
  const container = document.getElementById(CONTAINER_ID);
  if (!container) {
    console.log('未找到AI助手容器，跳过更新显示');
    return;
  }
  
  // 获取当前结果
  const currentResults = window.resumeManager.analysisResults || [];
  console.debug('当前结果数量:', currentResults.length);
  
  // 获取当前阈值
  const { scoreThreshold = 80 } = await new Promise(resolve => {
    chrome.storage.sync.get(['scoreThreshold'], resolve);
  });

  // 获取过滤器状态
  const filterCheckbox = container.querySelector('.qualified-filter');
  const showOnlyQualified = filterCheckbox?.checked || false;
  console.debug('过滤器状态:', showOnlyQualified ? '仅显示达标' : '显示全部');
  
  // 更新结果数量
  const resultsCount = container.querySelector('.results-count');
  if (resultsCount) {
    const qualifiedCount = currentResults.filter(r => (r.evaluation?.score || 0) >= scoreThreshold).length;
    resultsCount.textContent = showOnlyQualified 
      ? `已显示 ${qualifiedCount} / ${currentResults.length} 份达标简历` 
      : `已分析 ${currentResults.length} 份简历`;
  }
  
  // 获取或创建结果列表容器
  let resultsListContainer = container.querySelector('.results-list');
  if (!resultsListContainer) {
    console.log('创建新的结果列表容器');
    resultsListContainer = document.createElement('div');
    resultsListContainer.className = 'results-list';
    
    const resultsContainer = container.querySelector('.ai-resume-results');
    if (resultsContainer) {
      resultsContainer.appendChild(resultsListContainer);
    }
  }

  // 清空旧的结果
  resultsListContainer.innerHTML = '';
  
  // 按时间倒序显示结果，并应用过滤
  currentResults.slice().reverse().forEach(result => {
    const score = result.evaluation?.score || 0;
    const isQualified = score >= scoreThreshold;
    
    // 如果启用了过滤器且简历不达标，则跳过
    if (showOnlyQualified && !isQualified) {
      return;
    }

    const resultItem = document.createElement('div');
    resultItem.className = 'result-item';
    
    const latestWork = result.workExperiences?.[0] || {};
    
    // 创建基本信息区域
    const basicInfo = document.createElement('div');
    basicInfo.className = 'result-basic-info';
    
    // 左侧信息
    const basicInfoLeft = document.createElement('div');
    basicInfoLeft.className = 'basic-info-left';
    basicInfoLeft.innerHTML = `
      <span class="candidate-name">${result.name || '未知'}</span>
      <span class="candidate-info">
        ${result.age ? result.age + '岁' : ''} · 
        ${latestWork.company || '未知公司'}
      </span>
    `;
    
    // 右侧信息
    const basicInfoRight = document.createElement('div');
    basicInfoRight.className = 'basic-info-right';
    basicInfoRight.innerHTML = `
      <span class="score ${score >= scoreThreshold ? 'score-pass' : 'score-fail'}">${score}分</span>
      ${result.position ? `
        <button class="locate-button" title="定位到简历位置">
          <svg viewBox="0 0 24 24" width="14" height="14">
            <path fill="currentColor" d="M12 11.5A2.5 2.5 0 0 1 9.5 9 2.5 2.5 0 0 1 12 6.5 2.5 2.5 0 0 1 14.5 9a2.5 2.5 0 0 1-2.5 2.5M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7z"/>
          </svg>
        </button>` : ''}
      <svg class="expand-icon" viewBox="0 0 24 24" width="14" height="14">
        <path fill="currentColor" d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
      </svg>
    `;
    
    basicInfo.appendChild(basicInfoLeft);
    basicInfo.appendChild(basicInfoRight);
    resultItem.appendChild(basicInfo);
    
    // 创建详细信息区域
    const details = document.createElement('div');
    details.className = 'result-details';
    details.style.display = 'none';
    details.innerHTML = `
      ${result.evaluation?.suggestion ? `
        <div class="result-suggestion">${result.evaluation.suggestion}</div>` : ''}
      ${result.evaluation ? `
        <div class="result-score-details">
          <h4>评分明细：</h4>
          <div class="base-score">
            <span class="detail-key">基础分：</span>
            <span class="detail-value">${result.evaluation.details?.baseScore || 0}</span>
          </div>
          ${result.evaluation.details?.additions ? `
            <div class="additions-list">
              <div class="detail-key">评分标准：</div>
              <ul>
                ${Array.isArray(result.evaluation.details.additions) ? 
                  result.evaluation.details.additions.map(addition => `
                    <li class="addition-item">
                      <div class="score-rule">
                        <span class="rule-name">${addition.rule}</span>
                        <span class="score-value ${addition.score > 0 ? 'positive' : ''}">${addition.score}分</span>
                      </div>
                      <div class="score-reason">${addition.reason}</div>
                    </li>
                  `).join('') : 
                  `<li class="addition-item">无评分明细</li>`
                }
              </ul>
            </div>
          ` : ''}
        </div>` : ''}
      <div class="timestamp">分析时间：${new Date(result.timestamp).toLocaleString()}</div>
    `;
    resultItem.appendChild(details);
    
    // 为定位按钮添加独立的点击事件
    const locateButton = basicInfoRight.querySelector('.locate-button');
    if (locateButton) {
      locateButton.addEventListener('click', async (e) => {
        e.stopPropagation(); // 阻止事件冒泡
        
        // 获取所有iframe
        const iframes = Array.from(document.querySelectorAll('iframe'));
        let targetElement = null;
        let targetIframe = null;

        // 首先尝试从result中获取resumeId
        const resumeId = result.position?.resumeId;
        if (!resumeId) {
          console.warn('简历数据中缺少resumeId信息');
          return;
        }

        // 遍历所有iframe寻找目标元素
        for (const iframe of iframes) {
          try {
            if (iframe.contentDocument) {
              // 使用data-expect属性查找元素
              const element = iframe.contentDocument.querySelector(`a[data-expect='${resumeId}']`) ||
              iframe.contentDocument.querySelector(`div[data-geekid='${resumeId}']`) ||
              iframe.contentDocument.querySelector(`div[data-geek='${resumeId}']`) ||
              iframe.contentDocument.querySelector(`div[data-id='${resumeId}-0']`)
              
              
              if (element) {
                console.log('找到目标元素，resumeId:', resumeId);
                targetElement = element;
                targetIframe = iframe;
                break;
              } else {
                console.log('在当前iframe中未找到resumeId为', resumeId, '的元素');
              }
            }
          } catch (error) {
            console.log('在当前iframe中查找元素失败:', error);
            continue;
          }
        }

        const highlightStyle = `
          @keyframes highlight-border {
            0% {
              outline: 2px solid #1890ff;
              box-shadow: 0 0 8px #1890ff;
              background-color: rgba(24, 144, 255, 0.1);
            }
            50% {
              outline: 2px solid transparent;
              box-shadow: none;
              background-color: transparent;
            }
            100% {
              outline: 2px solid #1890ff;
              box-shadow: 0 0 8px #1890ff;
              background-color: rgba(24, 144, 255, 0.1);
            }
          }
          .highlight-element {
            animation: highlight-border 1s ease-in-out 3 !important;
            position: relative !important;
            z-index: 10000 !important;
          }
        `;
        
        // 首先尝试在 targetIframe 中查找，如果找不到则在主文档中查找
        const foundElement = targetIframe?.contentDocument?.querySelector(`a[data-expect='${resumeId}']`) ||
                           targetIframe?.contentDocument?.querySelector(`div[data-geekid='${resumeId}']`) ||
                           targetIframe?.contentDocument?.querySelector(`div[data-geek='${resumeId}']`) ||
                           targetIframe?.contentDocument?.querySelector(`div[data-id='${resumeId}-0']`) ||
                           document.querySelector(`a[data-expect='${resumeId}']`) ||
                           document.querySelector(`div[data-geekid='${resumeId}']`) ||
                           document.querySelector(`div[data-geek='${resumeId}']`) ||
                           document.querySelector(`div[data-id='${resumeId}-0']`);
        const targetDoc = targetIframe?.contentDocument || document;
        
        if (!foundElement) {
          console.log('未找到目标元素:', `a[data-expect='${resumeId}']`, `div[data-geekid='${resumeId}']`, `div[data-geek='${resumeId}']`, `div[data-id='${resumeId}-0']`);
          return;
        }
        
        // 检查是否已存在样式元素
        let styleElement = targetDoc.querySelector('#highlight-style');
        if (!styleElement) {
          styleElement = targetDoc.createElement('style');
          styleElement.id = 'highlight-style';
          targetDoc.head.appendChild(styleElement);
        }
        styleElement.textContent = highlightStyle;
        
        // 移除其他元素的高亮效果
        const highlightedElements = targetDoc.querySelectorAll('.highlight-element');
        highlightedElements.forEach(el => el.classList.remove('highlight-element'));
        
        // 滚动到元素位置
        foundElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 添加高亮效果
        foundElement.classList.add('highlight-element');
        
        // 3秒后移除高亮效果
        setTimeout(() => {
          foundElement.classList.remove('highlight-element');
        }, 3000);
      });
    }
    
    // 添加展开/折叠点击事件
    basicInfo.addEventListener('click', function(e) {
      // 如果点击的是定位按钮，不处理折叠逻辑
      if (e.target.closest('.locate-button')) {
        return;
      }
      
      // 展开/折叠详细信息
      const details = this.nextElementSibling;
      const expandIcon = this.querySelector('.expand-icon');
      if (details.style.display === 'none') {
        details.style.display = 'block';
        expandIcon.style.transform = 'rotate(180deg)';
      } else {
        details.style.display = 'none';
        expandIcon.style.transform = 'rotate(0)';
      }
    });
    
    resultsListContainer.appendChild(resultItem);
  });
  
  console.debug('结果显示更新完成');
}

// 修改下载结果函数
function downloadResults() {
  console.log('开始下载结果');
  chrome.storage.sync.get(['scoreThreshold'], ({ scoreThreshold = 80 }) => {
    // 对分析结果进行排序（按分数从高到低）
    const sortedResults = [...window.resumeManager.analysisResults].sort((a, b) => {
      const scoreA = a.evaluation?.score || 0;
      const scoreB = b.evaluation?.score || 0;
      return scoreB - scoreA;
    });

    // 计算统计信息
    const totalCount = sortedResults.length;
    const qualifiedCount = sortedResults.filter(r => r.evaluation?.score >= scoreThreshold).length;
    const averageScore = sortedResults.reduce((sum, r) => sum + (r.evaluation?.score || 0), 0) / totalCount;

    console.log('准备生成导出内容', {
      totalCount,
      qualifiedCount,
      averageScore,
      sortedResults
    });

    // 生成 HTML 内容
    const htmlContent = window.templates.exportTemplate({
      totalCount,
      qualifiedCount,
      averageScore,
      sortedResults,
      scoreThreshold
    });

    // 创建下载链接
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `AI简历分析结果_${new Date().toLocaleDateString()}.html`;
    
    console.log('触发下载');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  });
}

// 获取用户设置的评分阈值
function getUserScoreThreshold() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['scoreThreshold'], (result) => {
      resolve(result.scoreThreshold || 80);
    });
  });
}

// 获取当前环境设置
async function getCurrentEnvironment() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['apiEnvironment'], (result) => {
      resolve(result.apiEnvironment || 'development');
    });
  });
}

// 获取API密钥
async function getApiKey() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['apiKey'], (result) => {
      resolve(result.apiKey);
    });
  });
}

// 获取剩余积分
async function getCredit() {
  try {
    const environment = await getCurrentEnvironment();
    const apiKey = await getApiKey();
    
    if (!apiKey) {
      throw new Error('未设置API密钥');
    }
    
    const baseUrl = window.API_CONFIG[environment].baseUrl;
    const response = await fetch(`${baseUrl}/api/info`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.ok) {
      if (response.status === 403) {
        throw new Error('API密钥无效或已过期');
      }
      throw new Error(`获取积分信息失败 (状态码: ${response.status})`);
    }

    const data = await response.json();
    if (!data || typeof data.remainingCredits === 'undefined') {
      throw new Error('获取积分信息失败，返回数据格式不正确');
    }

    return data.remainingCredits;
  } catch (error) {
    console.error('获取积分出错:', error);
    throw error;
  }
}

// 添加快捷键支持
function addKeyboardShortcuts() {
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      stopAnalysis();
    }
  });
}

// 修改后的 startAIAnalysis 函数
async function startAIAnalysis() {
  // 如果正在分析中，则停止分析
  if (isAnalyzing) {
    console.log('停止当前分析');
    stopAnalysis();
    return;
  }

  // 防止重复启动
  if (analysisLock) {
    console.log('分析已被锁定，请等待');
    return;
  }

  // 检查配置是否完整
  const config = window.resumeManager.currentConfig;
  if (!config) {
    alert('配置信息未加载，请刷新页面重试');
    return;
  }

  // 异步检查API密钥
  async function checkApiKey() {
    try {
      // 尝试从storage.sync获取
      const syncData = await new Promise(resolve => {
        chrome.storage.sync.get(['apiKey'], resolve);
      });
      
      // 如果sync中没有，尝试从storage.local获取
      if (!syncData.apiKey) {
        const localData = await new Promise(resolve => {
          chrome.storage.local.get(['apiKey'], resolve);
        });
        
        if (!localData.apiKey) {
          console.error('未找到API密钥，请确认已正确配置');
          alert('请先在扩展设置中配置API密钥');
          return false;
        }
        
        // 找到了local中的密钥
        config.apiKey = localData.apiKey;
        return true;
      }
      
      // 找到了sync中的密钥
      config.apiKey = syncData.apiKey;
      return true;
    } catch (error) {
      console.error('检查API密钥时出错:', error);
      alert('检查API密钥时出错，请重试');
      return false;
    }
  }

  // 在使用API之前调用此函数
  const keyIsValid = await checkApiKey();
  if (!keyIsValid) {
    return; // 停止后续操作
  }

  // 检查评分阈值
  if (!config.scoreThreshold) {
    alert('请先在扩展设置中配置评分阈值');
    return;
  }

  const button = document.querySelector('.ai-resume-button');
  console.log('开始分析简历');
  isAnalyzing = true;

  try {
    const credit = await getCredit();
    if (credit <= 0) {
      alert('积分不足，请先充值');
      stopAnalysis();
      return;
    }
    
    if (button) {
      button.classList.add('processing');
      button.style.backgroundColor = '#ff4d4f';
      const randomMessage = workingMessages[Math.floor(Math.random() * workingMessages.length)];
      button.innerHTML = `
        <svg class="button-icon spinning" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z">
            <animateTransform attributeName="transform" type="rotate" from="0 12 12" to="360 12 12" dur="1s" repeatCount="indefinite"/>
          </path>
        </svg>
        AI正在努力工作...
      `;
    }
    
    // 加载已分析的简历ID
    await window.resumeManager.loadAnalyzedResumeIds();
    console.log('已加载分析记录');

    // 根据页面路径选择不同的处理函数
    const path = window.location.pathname;
    
    // 创建一个可取消的Promise
    let runAnalysis;
    
    const analysisPromise = new Promise(async (resolve, reject) => {
      try {
        // 存储当前运行的分析Promise，以便可以检查它是否被中断
        runAnalysis = { resolve, reject };
        
        // 选择并执行相应的处理函数
        if (path.includes('/web/chat/search')) {
          await handleSearchPageAnalysis(button);
        } else if (path.includes('/web/chat/index')) {
          await handleChatPageAnalysis(button);
        } else if (path.includes('/web/chat/recommend')) {
          await handleRecommendPageAnalysis(button);
        }
        resolve();
      } catch (error) {
        if (isAnalyzing) { // 只有在分析仍在进行时才报告错误
          console.error('分析过程中发生错误:', error);
          reject(error);
        } else {
          // 分析已停止，这是预期行为
          resolve();
        }
      }
    });
    
    // 监听分析状态变化
    const checkIsAnalyzing = setInterval(() => {
      if (!isAnalyzing && runAnalysis) {
        clearInterval(checkIsAnalyzing);
        // 正常结束当前执行的Promise
        runAnalysis.resolve();
        runAnalysis = null;
      }
    }, 500);
    
    // 等待分析完成或被中断
    await analysisPromise;
    
    // 清理
    clearInterval(checkIsAnalyzing);
    
  } catch (error) {
    console.error('分析过程出错:', error);
    if (isAnalyzing) { // 只有当仍在分析时才显示错误
      alert(error.message);
    }
    stopAnalysis();
  }
}

// 修改原来的 stopAnalysis 函数
function stopAnalysis() {
  console.log('停止分析流程');
  
  // 先设置标志，中断所有进行中的流程
  isAnalyzing = false;
  analysisLock = false;
  isScrolling = false;
  scrollStuckCount = 0;
  
  // 清除所有可能存在的定时器
  const timers = window.setTimeout(() => {}, 0);
  for (let i = 0; i < timers; i++) {
    window.clearTimeout(i);
  }
  
  // 重置按钮状态
  const button = document.querySelector('.ai-resume-button');
  if (button) {
    button.classList.remove('processing');
    button.classList.remove('success');
    button.style.backgroundColor = '';
    button.innerHTML = `
      <svg class="button-icon" viewBox="0 0 24 24" width="16" height="16">
        <path fill="currentColor" d="M13 24q-2.5 0-4.75-0.95t-3.875-2.575q-1.625-1.625-2.575-3.875t-0.95-4.75q0-2.5 0.95-4.75t2.575-3.875q1.625-1.625 3.875-2.575t4.75-0.95q2.5 0 4.75 0.95t3.875 2.575q1.625 1.625 2.575 3.875t0.95 4.75q0 2.5-0.95 4.75t-2.575 3.875q-1.625 1.625-3.875 2.575t-4.75 0.95zM13 22q2.075 0 3.9-0.788t3.175-2.137q1.35-1.35 2.137-3.175t0.788-3.9q0-2.075-0.788-3.9t-2.137-3.175q-1.35-1.35-3.175-2.137t-3.9-0.788q-2.075 0-3.9 0.788t-3.175 2.137q-1.35 1.35-2.138 3.175t-0.787 3.9q0 2.075 0.787 3.9t2.138 3.175q1.35 1.35 3.175 2.137t3.9 0.788zM13 18q-1.65 0-2.825-1.175t-1.175-2.825q0-1.65 1.175-2.825t2.825-1.175q1.65 0 2.825 1.175t1.175 2.825q0 1.65-1.175 2.825t-2.825 1.175zM13 16q0.825 0 1.413-0.588t0.587-1.412q0-0.825-0.587-1.413t-1.413-0.587q-0.825 0-1.412 0.587t-0.588 1.413q0 0.825 0.588 1.412t1.412 0.588z"/>
      </svg>
      AI读简历
    `;
  }
  
  // 尝试关闭所有可能打开的弹窗
  try {
    if (window.resumeManager && window.resumeManager.closeAllResumeDialogs) {
      window.resumeManager.closeAllResumeDialogs(document);
    }
  } catch (error) {
    console.error('关闭弹窗失败:', error);
  }
  
  console.log('分析已完全停止');
}

// 清空结果函数
function clearResults() {
  console.log('显示清空确认对话框');
  
  // 创建确认对话框
  const dialogContainer = document.createElement('div');
  dialogContainer.className = 'ai-resume-dialog-overlay';
  dialogContainer.innerHTML = `
    <div class="ai-resume-dialog">
      <div class="dialog-header">
        <h3>清空确认</h3>
      </div>
      <div class="dialog-content">
        <p>请选择清空的范围：</p>
        <div class="dialog-options">
          <label class="dialog-option-label">
            <input type="radio" name="clearOption" value="results" class="dialog-radio">
            <div class="dialog-option">
              <div class="option-title">仅清空分析结果</div>
              <div class="option-desc">重新评估时将跳过已评估的简历</div>
            </div>
          </label>
          <label class="dialog-option-label">
            <input type="radio" name="clearOption" value="all" class="dialog-radio">
            <div class="dialog-option">
              <div class="option-title">清空全部记录</div>
              <div class="option-desc">清空分析结果和历史分析记录，所有简历将重新评估</div>
            </div>
          </label>
        </div>
      </div>
      <div class="dialog-footer">
        <button class="dialog-button confirm" disabled>确认清空</button>
        <button class="dialog-button cancel">取消</button>
      </div>
    </div>
  `;

  document.body.appendChild(dialogContainer);

  // 添加事件监听
  const handleClearAction = (action) => {
    console.log('执行清空操作:', action);
    
    if (action === 'results') {
      // 仅清空分析结果
      window.resumeManager.analysisResults = [];
      chrome.storage.local.set({ 'analysisResults': [] }, () => {
        console.log('分析结果已清空');
        updateResultsDisplay();
      });
    } else if (action === 'all') {
      // 清空所有记录
      window.resumeManager.analysisResults = [];
      window.resumeManager.analyzedResumeIds.clear();
      chrome.storage.local.set({
        'analysisResults': [],
        'analyzedResumeIds': []
      }, () => {
        console.log('所有记录已清空');
        updateResultsDisplay();
      });
    }
    
    // 移除对话框
    document.body.removeChild(dialogContainer);
  };

  // 获取按钮和单选框元素
  const confirmButton = dialogContainer.querySelector('.confirm');
  const radioButtons = dialogContainer.querySelectorAll('input[name="clearOption"]');
  let selectedAction = null;

  // 添加单选框change事件
  radioButtons.forEach(radio => {
    radio.addEventListener('change', (e) => {
      selectedAction = e.target.value;
      confirmButton.disabled = false;
    });
  });

  // 绑定按钮事件
  confirmButton.addEventListener('click', () => {
    if (selectedAction) {
      handleClearAction(selectedAction);
    }
  });
  
  dialogContainer.querySelector('.cancel').addEventListener('click', () => {
    document.body.removeChild(dialogContainer);
  });
}

// 自动滚动函数
async function autoScroll(doc) {
  return new Promise((resolve) => {
    console.log('开始自动滚动');
    const scrollableElement = doc.documentElement;
    isScrolling = true;
    let scrollTimer = null;
    
    // 递归滚动函数
    function scrollDown() {
      // 如果分析已停止，终止滚动
      if (!isAnalyzing) {
        console.log('检测到分析停止，中断滚动');
        isScrolling = false;
        if (scrollTimer) clearTimeout(scrollTimer);
        resolve();
        return;
      }
      
      // 检查是否已到达底部
      const maxScroll = scrollableElement.scrollHeight - scrollableElement.clientHeight;
      const currentScrollTop = scrollableElement.scrollTop;
      
      // 检测滚动是否被卡住
      if (Math.abs(currentScrollTop - lastScrollPosition) < 5) {
        scrollStuckCount++;
        if (scrollStuckCount >= MAX_SCROLL_STUCK) {
          console.log('滚动被卡住，终止滚动');
          isScrolling = false;
          resolve();
          return;
        }
      } else {
        scrollStuckCount = 0;
      }
      lastScrollPosition = currentScrollTop;
      
      // 如果已经到达底部，结束滚动
      if (currentScrollTop >= maxScroll) {
        console.log('已到达页面底部');
        isScrolling = false;
        resolve();
        return;
      }
      
      // 执行滚动
      scrollableElement.scrollBy({
        top: 500,
        behavior: 'smooth'
      });
      
      // 等待滚动动画完成后继续下一次滚动
      if (scrollTimer) clearTimeout(scrollTimer);
      scrollTimer = setTimeout(() => {
        // 再次检查是否应该停止
        if (!isAnalyzing) {
          console.log('延时期间检测到分析停止，中断滚动');
          isScrolling = false;
          resolve();
          return;
        }
        scrollDown();
      }, 1000);
    }
    
    // 开始滚动
    scrollDown();
  });
}

// 等待对话框加载函数
function waitForDialog(doc = document, timeout = 10000) {
  return new Promise((resolve, reject) => {
    console.log('等待对话框加载');
    const startTime = Date.now();
    
    const checkDialog = () => {
      if (!isAnalyzing) {
        reject(new Error('用户中断操作'));
        return;
      }

      const dialog = doc.querySelector('.boss-dialog__wrapper') || doc.querySelector('.resume-detail-wrap') || doc.querySelector('div.boss-popup__content > div > div > div.resume-layout-wrap > div') || doc.querySelector('.dialog-container');
      if (dialog) {
        console.log('对话框已加载');
        resolve(dialog);
        return;
      }

      if (Date.now() - startTime > timeout) {
        reject(new Error('等待对话框超时'));
        return;
      }

      setTimeout(checkDialog, 1000);
    };

    checkDialog();
  });
}

// 等待 iframe 中元素的函数
function waitForElementInIframe(doc, selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const observer = new MutationObserver(() => {
      const element = doc.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });
    
    observer.observe(doc.documentElement, {
      childList: true,
      subtree: true
    });
    
    const element = doc.querySelector(selector);
    if (element) {
      observer.disconnect();
      resolve(element);
    }
    
    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`等待元素 ${selector} 超时`));
    }, timeout);
  });
}

function initializeUI() {
  // 创建并添加样式
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);

  // 创建主容器
  const container = document.createElement('div');
  container.className = 'ai-resume-container';
  container.innerHTML = template;
  document.body.appendChild(container);

  // 初始化收缩按钮事件
  const collapseButton = container.querySelector('.collapse-button');
  const collapseIcon = collapseButton.querySelector('.collapse-icon');
  const collapseText = collapseButton.querySelector('.collapse-text');
  const resultsContainer = container.querySelector('.ai-resume-results');
  const actionsContainer = container.querySelector('.results-actions');
  
  collapseButton.addEventListener('click', () => {
    container.classList.toggle('collapsed');
  });

  // 初始化错误容器事件
  const errorContainer = document.getElementById('errorContainer');
  const closeBtn = errorContainer.querySelector('.close-btn');
  closeBtn.addEventListener('click', () => {
    errorContainer.classList.remove('show');
  });

  // 初始化其他UI元素
  initializeAnalysisButton();
  initializePinButton();
  
  return container;
}

// 显示错误信息函数
function showError(message) {
  const errorContainer = document.getElementById('errorContainer');
  const errorMessage = document.getElementById('errorMessage');
  
  if (errorContainer && errorMessage) {
    errorMessage.textContent = message;
    errorContainer.classList.add('show');
  } else {
    console.error('错误提示容器未找到，错误信息：', message);
  }
}

async function handleMessage(request, sender, sendResponse) {
  if (analysisLock) {
    console.log('分析已在进行中，跳过新请求');
    return;
  }

  try {
    analysisLock = true;
    
    // 显示工作状态
    const container = document.querySelector('.ai-resume-container');
    if (container) {
      container.classList.remove('collapsed');
    }
    
    // 更新工作状态文案
    const messageInterval = setInterval(updateWorkingMessage, 2000);
    
    // 发送分析请求
    const response = await fetch(request.url);
    const data = await response.json();
    
    // 清除工作状态更新
    clearInterval(messageInterval);
    
    // 处理响应
    if (data.success) {
      // 更新结果显示
      const resultsList = document.querySelector('.results-list');
      if (resultsList) {
        resultsList.innerHTML = '分析完成！结果：' + JSON.stringify(data.results);
      }
      sendResponse({ success: true });
    } else {
      showError('分析失败：' + (data.error || '未知错误'));
      sendResponse({ success: false, error: data.error });
    }
  } catch (error) {
    console.error('处理消息时发生错误:', error);
    showError('处理请求时发生错误');
    sendResponse({ success: false, error: error.message });
  } finally {
    analysisLock = false;
  }
}

// 更新工作状态文案
function updateWorkingMessage() {
  const messageElement = document.querySelector('.working-message');
  if (messageElement) {
    const randomIndex = Math.floor(Math.random() * workingMessages.length);
    messageElement.textContent = workingMessages[randomIndex];
  }
}

// 初始化
init();