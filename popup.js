// API配置
window.API_CONFIG = {
  development: {
    baseUrl: 'http://localhost:8787',
  },
  production: {
      baseUrl: 'http://127.0.0.1:8081',
  }
};

// 当popup页面加载完成时，加载已保存的设置
document.addEventListener('DOMContentLoaded', async () => {
  // 获取所有设置元素
  const apiKeyInput = document.getElementById('apiKey');
  const scoreThresholdInput = document.getElementById('scoreThreshold');
  const autoFavoriteCheckbox = document.getElementById('autoFavorite');
  const autoGreetCheckbox = document.getElementById('autoGreet');
  const autoShareCheckbox = document.getElementById('autoShare');
  const saveButton = document.getElementById('saveSettings');
  const startButton = document.getElementById('startNow');
  const statusDiv = document.getElementById('status');
  const modelSelect = document.getElementById('model');
  const useCustomEndpointCheckbox = document.getElementById('useCustomEndpoint');
  const customEndpointSection = document.getElementById('customEndpointSection');
  
  // 添加立即开始按钮的点击事件
  startButton.addEventListener('click', () => {
    chrome.tabs.create({ url: 'https://www.zhipin.com/web/chat/search' });
  });

  // 添加错误信息关闭按钮的点击事件
  const closeBtn = document.querySelector('.error-container .close-btn');
  closeBtn.addEventListener('click', () => {
    hideError();
  });

  // 添加自定义端点复选框的变更事件
  useCustomEndpointCheckbox.addEventListener('change', async () => {
    const useCustom = useCustomEndpointCheckbox.checked;
    
    if (useCustom) {
      // 如果启用自定义端点，从服务器获取最新的模型列表
      try {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
          showStatus('请先输入API密钥', 'error');
          useCustomEndpointCheckbox.checked = false;
          return;
        }
        
        const environment = await getCurrentEnvironment();
        const baseUrl = window.API_CONFIG[environment].baseUrl;
        
        showStatus('正在获取可用模型列表...', 'info');
        
        // 请求API获取最新信息
        const response = await fetch(`${baseUrl}/api/info`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });
        
        if (!response.ok) {
          throw new Error(`获取API信息失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('获取到的最新API配置:', data);
        
        if (!data.allowedModels || !Array.isArray(data.allowedModels) || data.allowedModels.length === 0) {
          throw new Error('服务端未返回有效的模型列表');
        }
        
        // 使用服务端返回的模型列表和自定义端点
        const allowedModels = data.allowedModels;
        const defaultModel = allowedModels[0];
        const customEndpoint = data.customEndpoint || '';
        
        // 更新模型下拉框
        updateModelSelectOptions(allowedModels, defaultModel);
        
        // 保存设置
        await new Promise(resolve => {
          chrome.storage.sync.set({
            useCustomEndpoint: true,
            allowedModels: allowedModels,
            model: defaultModel,
            customEndpoint: customEndpoint
          }, resolve);
        });
        
        // 更新自定义端点选项的显示状态
        updateCustomEndpointSectionVisibility(customEndpoint);
        
        showStatus('已更新可用模型列表', 'success');
      } catch (error) {
        console.error('获取自定义端点模型列表失败:', error);
        showStatus(`获取自定义端点模型列表失败: ${error.message}`, 'error');
        
        // 出错时回退到未选中状态
        useCustomEndpointCheckbox.checked = false;
        return;
      }
    } else {
      // 如果禁用自定义端点，使用默认模型列表
      const allowedModels = ["deepseek-v3", "qwq-plus", "gemini-flash"];
      const defaultModel = 'gemini-flash';
      
      // 更新模型下拉框
      updateModelSelectOptions(allowedModels, defaultModel);
      
      // 保存设置
      await new Promise(resolve => {
        chrome.storage.sync.set({
          useCustomEndpoint: false,
          allowedModels: allowedModels,
          model: defaultModel
        }, resolve);
      });
      
      showStatus('已切换到默认模型列表', 'success');
    }
  });

  // API密钥管理相关函数
  // 初始化API密钥和获取积分信息
  async function initApiKey() {
    const result = await new Promise(resolve => {
      chrome.storage.sync.get(['apiKey', 'apiEnvironment'], resolve);
    });
    
    const defaultApiKey = 'trial-token-123';
    const apiKey = result.apiKey || defaultApiKey;
    const environment = result.apiEnvironment || 'production';  // 默认使用生产环境
    
    // 确保设置了环境和默认API密钥
    await new Promise(resolve => {
      chrome.storage.sync.set({ 
        apiEnvironment: environment,
        apiKey: apiKey  // 保存API密钥（如果是默认值也保存）
      }, resolve);
    });
    
    // 设置输入框值
    apiKeyInput.value = apiKey;
    
    // 获取积分信息
    await fetchCreditsInfo(apiKey, environment);
    
    return { apiKey, environment };
  }
  
  // 更新API密钥并获取积分信息
  async function updateApiKey(newApiKey) {
    const environment = await getCurrentEnvironment();
    
    // 如果密钥为空，隐藏积分信息
    if (!newApiKey) {
      hideCreditsInfo();
      return;
    }
    
    // 获取积分信息
    await fetchCreditsInfo(newApiKey, environment);
  }
  
  // 保存API密钥到存储
  function saveApiKey(apiKey) {
    return new Promise(resolve => {
      chrome.storage.sync.set({ apiKey }, resolve);
    });
  }
  
  // 初始化设置
  async function initSettings() {
    const result = await new Promise(resolve => {
      chrome.storage.sync.get([
        'scoreThreshold',
        'autoFavorite',
        'autoGreet',
        'model',
        'autoShare',
        'customEndpoint',
        'allowedModels',
        'useCustomEndpoint'
      ], resolve);
    });

    // 从local storage获取模板配置
    const templateResult = await new Promise(resolve => {
      chrome.storage.local.get([
        'promptTemplates',
        'selectedPromptIndex'
      ], resolve);
    });
    
    // 检查是否使用自定义端点
    const useCustomEndpoint = result.useCustomEndpoint !== undefined ? result.useCustomEndpoint : false;
    
    // 根据是否使用自定义端点设置模型和允许的模型列表
    const defaultAllowedModels = useCustomEndpoint ? 
      (result.allowedModels || ['qwq-plus']) : 
      ["deepseek-v3", "qwq-plus", "gemini-flash"];
    
    const defaultModel = useCustomEndpoint ? 
      (result.model || 'qwq-plus') : 
      (result.model || 'gemini-flash');
    
    // 设置默认值并保存到存储
    const defaultSettings = {
      scoreThreshold: result.scoreThreshold || 80,
      autoFavorite: result.autoFavorite !== undefined ? result.autoFavorite : true,
      autoGreet: result.autoGreet !== undefined ? result.autoGreet : false,
      autoShare: result.autoShare !== undefined ? result.autoShare : false,
      model: defaultModel,
      customEndpoint: result.customEndpoint || '',
      allowedModels: defaultAllowedModels,
      useCustomEndpoint: useCustomEndpoint
    };

    // 保存基本设置到sync storage
    await new Promise(resolve => {
      chrome.storage.sync.set(defaultSettings, resolve);
    });

    // 保存模板设置到local storage
    const defaultTemplateSettings = {
      promptTemplates: templateResult.promptTemplates || [{
        name: '默认模板',
        content: `请根据以下标准评估这份简历（默认0分）：

基础分（60分）：
1. 工作经历中有ToB公司经验（+20分）
2. 简历中提到AI相关技能或经验（+10分）
3. 有独立文案、视频剪辑制作能力或相关工具使用经验（+10分）
4. 有线上、线下活动运营经验（+10分）
5. 有大型互联网公司工作经验（+10分）

加分项（最多40分）：
1. 学历为本科及以上（+15分）
2. 最近5年工作稳定性：
   - 3家以内公司（+10分）
   - 3-5家公司（+5分）
   - 5家以上公司（-5分）
3. 有相关行业经验（+15分）：
   - 网络安全/信息安全行业
   - 企业服务行业
   - 人工智能行业

请详细说明每项得分原因，并给出改进建议。`
      }],
      selectedPromptIndex: templateResult.selectedPromptIndex || 0
    };

    await new Promise(resolve => {
      chrome.storage.local.set(defaultTemplateSettings, resolve);
    });
    
    // 更新UI显示
    scoreThresholdInput.value = defaultSettings.scoreThreshold;
    autoFavoriteCheckbox.checked = defaultSettings.autoFavorite;
    autoGreetCheckbox.checked = defaultSettings.autoGreet;
    autoShareCheckbox.checked = defaultSettings.autoShare;
    useCustomEndpointCheckbox.checked = defaultSettings.useCustomEndpoint;
    
    // 更新自定义端点选项的显示状态
    updateCustomEndpointSectionVisibility(defaultSettings.customEndpoint);
    
    // 更新模型选择下拉框
    updateModelSelectOptions(defaultSettings.allowedModels, defaultSettings.model);
    
    // 初始化API密钥和积分信息
    await initApiKey();
  }

  // 更新模型选择下拉框
  function updateModelSelectOptions(allowedModels, selectedModel) {
    // 清空当前选项
    modelSelect.innerHTML = '';
    
    // 添加新选项
    if (Array.isArray(allowedModels) && allowedModels.length > 0) {
      allowedModels.forEach(model => {
        const option = document.createElement('option');
        option.value = model;
        option.textContent = model;
        modelSelect.appendChild(option);
      });
    } else {
      // 如果没有可用模型，添加默认选项
      const option = document.createElement('option');
      option.value = 'qwq-plus';
      option.textContent = 'qwq-plus';
      modelSelect.appendChild(option);
    }
    
    // 设置选中的模型
    if (selectedModel && allowedModels.includes(selectedModel)) {
      modelSelect.value = selectedModel;
    } else if (allowedModels && allowedModels.length > 0) {
      modelSelect.value = allowedModels[0];
    }
  }
  
  // 初始化环境设置
  async function initEnvironment() {
    // 检查是否是开发环境
    const isDevelopment = chrome.runtime.id === 'hgnloggcdicncjfkhdgfkbhgdpmpbhjm'; // 替换为你的开发环境扩展ID

    // 获取环境切换开关的容器
    const envSwitchContainer = document.querySelector('.setting-item.env-switch');
    
    // 只在开发环境显示环境切换开关
    if (!isDevelopment) {
      envSwitchContainer.style.display = 'none';
      // 确保使用生产环境API
      await new Promise(resolve => {
        chrome.storage.sync.set({ apiEnvironment: 'production' }, resolve);
      });
      return;
    }
    
    // 加载当前环境设置
    const result = await new Promise(resolve => {
      chrome.storage.sync.get(['apiEnvironment'], resolve);
    });
    
    const env = result.apiEnvironment || 'production';
    
    // 更新按钮状态
    const buttons = document.querySelectorAll('.env-button');
    buttons.forEach(button => {
      if (button.dataset.env === env) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
    
    // 监听环境切换
    buttons.forEach(button => {
      button.addEventListener('click', async () => {
        const newEnv = button.dataset.env;
        
        // 更新按钮状态
        buttons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // 保存环境设置
        await new Promise(resolve => {
          chrome.storage.sync.set({ apiEnvironment: newEnv }, resolve);
        });
        
        showStatus(`已切换到${newEnv === 'prod' ? '生产' : '开发'}环境`, 'success');
        
        // 切换环境后重新获取积分信息
        const apiKey = apiKeyInput.value.trim();
        if (apiKey) {
          await fetchCreditsInfo(apiKey, newEnv);
        }
      });
    });
  }

  // 按照正确的顺序初始化
  try {
    // 1. 先初始化环境设置
    await initEnvironment();
    // 2. 然后初始化其他设置
    await initSettings();
    // 3. 最后初始化模板
    initializeTemplates();
  } catch (error) {
    console.error('初始化失败:', error);
    showError('初始化设置失败，请刷新页面重试');
  }

  // 监听API密钥输入变化（使用防抖处理，避免频繁请求）
  let debounceTimer;
  apiKeyInput.addEventListener('input', () => {
    clearTimeout(debounceTimer);
    const apiKey = apiKeyInput.value.trim();
    
    // 延迟500毫秒，避免用户输入过程中频繁发送请求
    debounceTimer = setTimeout(() => {
      updateApiKey(apiKey);
    }, 500);
  });

  // 保存设置
  saveButton.addEventListener('click', async () => {
    const settings = {
      apiKey: apiKeyInput.value.trim(),
      scoreThreshold: parseInt(scoreThresholdInput.value, 10),
      autoFavorite: autoFavoriteCheckbox.checked,
      autoGreet: autoGreetCheckbox.checked,
      autoShare: autoShareCheckbox.checked,
      model: modelSelect.value,
      useCustomEndpoint: useCustomEndpointCheckbox.checked
    };

    // 验证输入
    if (!settings.apiKey) {
      showStatus('请输入API密钥', 'error');
      return;
    }

    if (isNaN(settings.scoreThreshold) || settings.scoreThreshold < 0 || settings.scoreThreshold > 100) {
      showStatus('评分阈值必须在0-100之间', 'error');
      return;
    }

    // 保存到Chrome存储
    try {
      await new Promise((resolve, reject) => {
        chrome.storage.sync.set(settings, () => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve();
          }
        });
      });
      
      showStatus('设置已保存', 'success');
      
      // 保存成功后更新积分信息
      const environment = await getCurrentEnvironment();
      await fetchCreditsInfo(settings.apiKey, environment);
    } catch (error) {
      showStatus('保存失败：' + error.message, 'error');
    }
  });

  // 显示状态信息
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = 'status ' + type;
    setTimeout(() => {
      statusDiv.className = 'status';
    }, 3000);
  }

  // 模板管理相关变量
  let promptTemplates = [];
  let selectedTemplateIndex = 0;

  // 初始化模板列表
  function initializeTemplates() {
    chrome.storage.local.get(['promptTemplates', 'selectedPromptIndex'], (result) => {
      promptTemplates = result.promptTemplates || [{
        name: '默认模板',
        content: `请根据以下标准评估这份简历（默认0分）：

基础分（60分）：
1. 工作经历中有ToB公司经验（+20分）
2. 简历中提到AI相关技能或经验（+10分）
3. 有独立文案、视频剪辑制作能力或相关工具使用经验（+10分）
4. 有线上、线下活动运营经验（+10分）
5. 有大型互联网公司工作经验（+10分）

加分项（最多40分）：
1. 学历为本科及以上（+15分）
2. 最近5年工作稳定性：
   - 3家以内公司（+10分）
   - 3-5家公司（+5分）
   - 5家以上公司（-5分）
3. 有相关行业经验（+15分）：
   - 网络安全/信息安全行业
   - 企业服务行业
   - 人工智能行业

请详细说明每项得分原因，并给出改进建议。`
      }];
      selectedTemplateIndex = result.selectedPromptIndex || 0;
      renderTemplateList();
    });
  }

  // 渲染模板列表
  function renderTemplateList() {
    const templateList = document.getElementById('template-list');
    templateList.innerHTML = '';
    
    promptTemplates.forEach((template, index) => {
      const templateItem = document.createElement('div');
      templateItem.className = `template-item ${index === selectedTemplateIndex ? 'selected' : ''}`;
      
      templateItem.innerHTML = `
        <div class="template-name">${template.name}</div>
        <div class="template-actions">
          <button class="small-button edit-template">编辑</button>
          <button class="small-button delete delete-template" ${promptTemplates.length === 1 ? 'disabled' : ''}>删除</button>
        </div>
      `;
      
      // 点击选择模板
      templateItem.querySelector('.template-name').addEventListener('click', () => {
        const oldIndex = selectedTemplateIndex;
        selectedTemplateIndex = index;
        chrome.storage.local.set({ selectedPromptIndex: index }, () => {
          renderTemplateList();
          showToast(`已切换到模板：${template.name}`);
        });
      });
      
      // 编辑模板
      templateItem.querySelector('.edit-template').addEventListener('click', () => {
        showTemplateEditor(template, index);
      });
      
      // 删除模板
      templateItem.querySelector('.delete-template').addEventListener('click', () => {
        if (promptTemplates.length > 1) {
          promptTemplates.splice(index, 1);
          if (selectedTemplateIndex >= index) {
            selectedTemplateIndex = Math.max(0, selectedTemplateIndex - 1);
          }
          saveTemplates();
        }
      });
      
      templateList.appendChild(templateItem);
    });
  }

  // 显示模板编辑器
  function showTemplateEditor(template = { name: '', content: '' }, editIndex = -1) {
    const editor = document.querySelector('.template-editor');
    const nameInput = document.getElementById('template-name');
    const contentInput = document.getElementById('template-content');
    
    editor.style.display = 'block';
    nameInput.value = template.name;
    contentInput.value = template.content;
    
    // 保存模板
    document.getElementById('save-template').onclick = () => {
      const name = nameInput.value.trim();
      const content = contentInput.value.trim();
      
      if (!name || !content) {
        alert('模板名称和内容不能为空');
        return;
      }
      
      if (editIndex >= 0) {
        promptTemplates[editIndex] = { name, content };
      } else {
        promptTemplates.push({ name, content });
        selectedTemplateIndex = promptTemplates.length - 1;
      }
      
      saveTemplates();
      editor.style.display = 'none';
    };
    
    // 取消编辑
    document.getElementById('cancel-template').onclick = () => {
      editor.style.display = 'none';
    };
  }

  // 保存模板到存储
  function saveTemplates() {
    chrome.storage.local.set({
      promptTemplates,
      selectedPromptIndex: selectedTemplateIndex
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('保存模板失败:', chrome.runtime.lastError);
        showError('保存模板失败，请重试');
      } else {
        console.log('模板保存成功');
        renderTemplateList();
      }
    });
  }

  // 添加新模板按钮事件
  document.getElementById('add-template').addEventListener('click', () => {
    showTemplateEditor();
  });

  // 更新自定义端点选项的显示状态
  function updateCustomEndpointSectionVisibility(customEndpoint) {
    if (customEndpoint) {
      customEndpointSection.style.display = 'flex';
    } else {
      customEndpointSection.style.display = 'none';
    }
  }
});

// 获取当前环境
async function getCurrentEnvironment() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['apiEnvironment'], (result) => {
      resolve(result.apiEnvironment || 'development');
    });
  });
}

// 显示错误信息
function showError(message) {
  console.log('显示错误信息:', message);
  const errorContainer = document.getElementById('errorContainer');
  const errorMessage = document.getElementById('errorMessage');
  
  if (!errorContainer || !errorMessage) {
    console.error('错误信息容器不存在:', {
      errorContainer: !!errorContainer,
      errorMessage: !!errorMessage
    });
    return;
  }
  
  errorMessage.textContent = message;
  errorContainer.classList.add('show');
}

// 隐藏错误信息
function hideError() {
  console.log('隐藏错误信息');
  const errorContainer = document.getElementById('errorContainer');
  const errorMessage = document.getElementById('errorMessage');
  
  if (!errorContainer || !errorMessage) {
    console.error('错误信息容器不存在:', {
      errorContainer: !!errorContainer,
      errorMessage: !!errorMessage
    });
    return;
  }
  
  errorContainer.classList.remove('show');
  errorMessage.textContent = '';
}

// 获取积分信息
async function fetchCreditsInfo(apiKey, environment) {
  if (!apiKey) {
    hideCreditsInfo();
    return;
  }
  
  try {
    if (!window.API_CONFIG || !window.API_CONFIG[environment]) {
      showError('API配置未正确加载，请刷新页面重试');
      hideCreditsInfo();
      return;
    }

    const baseUrl = window.API_CONFIG[environment].baseUrl;
    console.log('获取积分信息 - 环境:', environment, '使用API密钥:', apiKey);
    
    // 取消旧的错误提示
    hideError();
    
    // 添加请求调试信息
    console.log('请求API信息:', `${baseUrl}/api/info`);
    
    const response = await fetch(`${baseUrl}/api/info`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.ok) {
      if (response.status === 403) {
        showError('API密钥无效或已过期');
      } else {
        showError(`获取积分信息失败 (状态码: ${response.status})`);
      }
      hideCreditsInfo();
      return;
    }

    const data = await response.json();
    
    // 添加详细的日志输出
    console.log('服务器返回的完整数据:', JSON.stringify(data, null, 2));
    
    try {
      // 获取当前的useCustomEndpoint设置
      const { useCustomEndpoint } = await new Promise(resolve => {
        chrome.storage.sync.get(['useCustomEndpoint'], resolve);
      });

      // 保存自定义端点和可用模型到存储
      if (data.customEndpoint || data.allowedModels) {
        console.log('获取到新的API配置:', {
          customEndpoint: data.customEndpoint,
          allowedModels: data.allowedModels,
          useCustomEndpoint: useCustomEndpoint
        });

        // 如果服务器返回了模型列表，则使用服务器的列表；否则使用默认值
        const allowedModels = data.allowedModels && Array.isArray(data.allowedModels) && data.allowedModels.length > 0 ?
          data.allowedModels :
          (useCustomEndpoint ? ['qwq-plus'] : ["deepseek-v3", "qwq-plus", "gemini-flash"]);

        // 选择默认模型：优先使用服务器返回的第一个模型，否则根据端点类型选择
        const defaultModel = data.allowedModels && data.allowedModels.length > 0 ?
          data.allowedModels[0] :
          (useCustomEndpoint ? 'qwq-plus' : 'gemini-flash');

        // 如果服务器返回了自定义端点，但当前未启用自定义端点，则自动启用
        let shouldUseCustomEndpoint = useCustomEndpoint;
        if (data.customEndpoint && !useCustomEndpoint) {
          shouldUseCustomEndpoint = true;
          console.log('检测到服务器返回自定义端点，自动启用自定义端点功能');

          // 更新UI中的复选框状态
          const useCustomEndpointCheckbox = document.getElementById('useCustomEndpoint');
          if (useCustomEndpointCheckbox) {
            useCustomEndpointCheckbox.checked = true;
          }
        }

        await new Promise(resolve => {
          chrome.storage.sync.set({
            customEndpoint: data.customEndpoint || '',
            allowedModels: allowedModels,
            model: defaultModel,  // 同时更新选中的模型
            useCustomEndpoint: shouldUseCustomEndpoint
          }, resolve);
        });

        // 更新自定义端点选项的显示状态
        updateCustomEndpointSectionVisibility(data.customEndpoint);

        // 更新模型选择下拉框，使用新的模型列表和默认模型
        const modelSelect = document.getElementById('model');
        if (modelSelect) {
          updateModelSelectOptions(allowedModels, defaultModel);
        }

        // 如果自动启用了自定义端点，显示提示信息
        if (data.customEndpoint && !useCustomEndpoint) {
          showStatus('检测到新的API密钥支持自定义端点，已自动启用', 'success');
        }
      }
    } catch (configError) {
      console.error('处理API配置时出错:', configError);
      // 配置处理出错不影响积分信息显示
    }
    
    // 先调用显示积分信息，再隐藏错误信息
    try {
      updateCreditsDisplay(data);
      hideError(); // 只在成功更新积分显示后隐藏错误
    } catch (displayError) {
      console.error('显示积分信息时出错:', displayError);
      showError('显示积分信息时出错，请刷新页面重试');
    }
  } catch (error) {
    console.error('获取积分信息出错:', error);
    showError('无法连接到服务器，请检查网络连接或稍后重试');
    hideCreditsInfo();
  }
}

// 更新积分显示
function updateCreditsDisplay(data) {
  console.log('开始更新积分显示, 收到的数据:', data);
  
  const creditsInfo = document.querySelector('.credits-info');
  const remainingCredits = document.getElementById('remainingCredits');
  const creditsWarning = document.getElementById('creditsWarning');
  
  // 检查DOM元素是否存在
  if (!creditsInfo || !remainingCredits || !creditsWarning) {
    console.error('积分信息显示所需的DOM元素不存在:', {
      creditsInfo: !!creditsInfo,
      remainingCredits: !!remainingCredits,
      creditsWarning: !!creditsWarning
    });
    return;
  }
  
  // 更详细的数据验证
  if (!data) {
    console.error('传入的数据为空');
    showError('获取积分信息失败，返回数据为空');
    hideCreditsInfo();
    return;
  }
  
  if (typeof data.totalCredits === 'undefined') {
    console.error('数据缺少totalCredits字段:', data);
    showError('获取积分信息失败，返回数据格式不正确');
    hideCreditsInfo();
    return;
  }
  
  // 确保所有需要的DOM元素都存在
  const totalCreditsEl = document.getElementById('totalCredits');
  const usedCreditsEl = document.getElementById('usedCredits');
  const expiresAtEl = document.getElementById('expiresAt');
  
  if (!totalCreditsEl || !usedCreditsEl || !expiresAtEl) {
    console.error('积分信息显示所需的某些DOM元素不存在:', {
      totalCreditsEl: !!totalCreditsEl,
      usedCreditsEl: !!usedCreditsEl,
      expiresAtEl: !!expiresAtEl
    });
    showError('页面元素加载不完整，请刷新重试');
    return;
  }

  // 更新显示
  try {
    totalCreditsEl.textContent = data.totalCredits;
    usedCreditsEl.textContent = data.usedCredits;
    remainingCredits.textContent = data.remainingCredits;
    expiresAtEl.textContent = data.expiresAt;

    // 显示积分信息区域
    creditsInfo.style.display = 'block';

    // 积分不足时显示警告
    if (data.remainingCredits < 20) {
      remainingCredits.classList.add('warning');
      creditsWarning.style.display = 'block';
      showError('积分即将用完，请及时充值');
    } else {
      remainingCredits.classList.remove('warning');
      creditsWarning.style.display = 'none';
    }
    
    console.log('积分信息更新成功');
  } catch (error) {
    console.error('更新积分显示时发生错误:', error);
    showError('更新积分显示时发生错误，请刷新重试');
  }
}

// 隐藏积分信息
function hideCreditsInfo() {
  console.log('隐藏积分信息');
  const creditsInfo = document.querySelector('.credits-info');
  
  if (!creditsInfo) {
    console.error('积分信息容器不存在');
    return;
  }
  
  creditsInfo.style.display = 'none';
}

// 显示提示消息
function showToast(message) {
  const toast = document.createElement('div');
  toast.className = 'toast-message';
  toast.textContent = message;
  document.body.appendChild(toast);
  
  // 淡入效果
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);
  
  // 2秒后淡出并移除
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 2000);
} 