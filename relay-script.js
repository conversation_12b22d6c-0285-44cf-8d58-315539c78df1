// 在推荐页面iframe中执行的中继脚本
(function() {
  console.log('中继脚本开始执行，location:', window.location.href);
  
  // 性能优化：使用节流函数减少频繁操作
  function throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }
  
  // 自动滚动到底部的函数
  async function autoScrollToBottom() {
    console.log('开始自动滚动到底部');
    
    // 等待页面完全加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 尝试多个可能的滚动容器
    const scrollSelectors = [
      '.resume-detail-wrap',
      '.resume-detail',
      '.detail-content',
      'body',
      'html'
    ];
    
    let scrollContainer = null;
    for (const selector of scrollSelectors) {
      scrollContainer = document.querySelector(selector);
      if (scrollContainer) {
        console.log('找到滚动容器:', selector);
        break;
      }
    }
    
    if (!scrollContainer) {
      console.log('未找到可滚动容器，使用window');
      scrollContainer = window;
    }
    
    // 执行滚动到底部
    await scrollToBottom(scrollContainer);
    
    console.log('自动滚动到底部完成');
  }
  
  // 替换原有optimizedScroll为滚动到底的实现
  async function scrollToBottom(scrollContainer) {
    console.log('开始滚动到底部，容器:', scrollContainer);
    
    let lastScrollTop = -1;
    let stuckCount = 0;
    const MAX_STUCK = 3;
    let scrollCount = 0;
    const MAX_SCROLL_COUNT = 20; // 防止无限滚动
    
    while (scrollCount < MAX_SCROLL_COUNT) {
      scrollCount++;
      
      let maxScroll, currentScroll;
      
      if (scrollContainer === window) {
        maxScroll = document.body.scrollHeight - window.innerHeight;
        currentScroll = window.pageYOffset || document.documentElement.scrollTop;
      } else {
        maxScroll = scrollContainer.scrollHeight - scrollContainer.clientHeight;
        currentScroll = scrollContainer.scrollTop;
      }
      
      console.log(`滚动第${scrollCount}次，当前位置: ${currentScroll}, 最大位置: ${maxScroll}`);
      
      // 检查是否已经到达底部
      if (Math.abs(currentScroll - maxScroll) < 10) {
        console.log('已到达页面底部');
        break;
      }
      
      // 检查是否卡住
      if (currentScroll === lastScrollTop) {
        stuckCount++;
        console.log(`滚动卡住，计数: ${stuckCount}`);
        if (stuckCount >= MAX_STUCK) {
          console.log('滚动已卡住，停止滚动');
          break;
        }
      } else {
        stuckCount = 0;
      }
      
      lastScrollTop = currentScroll;
      
      // 执行滚动
      if (scrollContainer === window) {
        window.scrollTo(0, Math.min(currentScroll + 500, maxScroll));
      } else {
        scrollContainer.scrollTop = Math.min(currentScroll + 500, maxScroll);
      }
      
      // 等待滚动完成
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    // 最后再等一会，确保内容加载
    await new Promise(resolve => setTimeout(resolve, 800));
    console.log('滚动完成，等待内容渲染');
  }
  
  // 标记是否已经开始滚动
  let hasStartedScrolling = false;
  
  // 监听来自简历iframe的消息并转发到顶层窗口
  window.addEventListener('message', async function(event) {
    // console.log('relay-script.js 收到消息:', event.data);
    
    // 检查是否需要开始自动滚动
    if (!hasStartedScrolling && event.data && 
        (event.data.type === 'IFRAME_DONE' || event.data.type === 'FIRST_LAYOUT')) {
      console.log('检测到内容渲染完成，开始自动滚动');
      hasStartedScrolling = true;
      
      // 延迟一点时间确保内容完全渲染
      setTimeout(async () => {
        await autoScrollToBottom();
      }, 500);
    }
    
    // 监听页面URL变化，重置滚动标记
    if (event.data && event.data.type === 'PAGE_CHANGED') {
      console.log('检测到页面变化，重置滚动标记');
      hasStartedScrolling = false;
    }
    
    // 监听简历iframe重新加载，重置滚动标记
    if (event.data && event.data.type === 'IFRAME_LOADED') {
      console.log('检测到iframe重新加载，重置滚动标记');
      hasStartedScrolling = false;
    }
    
    if (event.data && 
        event.data.type === 'resumeScrollRequest' && 
        event.data.source === 'resume-frame-script') {
      
      console.log('收到滚动请求');
      
      // 获取可滚动容器
      const scrollContainer = document.querySelector('.resume-detail-wrap');
      console.log('scrollContainer:', scrollContainer);
      if (!scrollContainer) {
        console.error('未找到可滚动容器');
        return;
      }
      
      // 执行优化的滚动
      await scrollToBottom(scrollContainer);
      
      // 发送滚动完成消息
      const response = {
        type: 'resumeScrollComplete',
        source: 'parent'
      };
      
      // 向所有iframe发送完成消息
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        try {
          iframe.contentWindow.postMessage(response, '*');
        } catch (e) {
          console.error('发送滚动完成消息失败:', e);
        }
      });
      
      console.log('滚动完成消息已发送');
    } else {
      // 处理其他消息的原有逻辑 - 优化消息处理
      if (event.data && event.data.source === 'resume-frame-script' && event.data.type === 'resumeTextExtracted') {
        console.log('准备转发resume-frame-script的消息');
        
        try {
          // 确保消息包含必要的信息
          if (!event.data.data || !event.data.data.text) {
            console.log('消息中没有文本内容，跳过转发');
            return;
          }

          // 添加中继信息
          const relayedMessage = {
            ...event.data,
            data: {
              ...event.data.data,
              relayedFrom: window.location.href,
              relayTimestamp: new Date().toISOString()
            }
          };
          
          // 先发送到顶层窗口
          window.top.postMessage(relayedMessage, '*');
          console.log('消息已发送到顶层窗口');
          
          // 再发送到父窗口（如果不是顶层窗口）
          if (window.parent !== window.top) {
            window.parent.postMessage(relayedMessage, '*');
            console.log('消息已发送到父窗口');
          }
          
          console.log('消息转发完成，文本长度:', event.data.data.textLength);
        } catch (e) {
          console.error('转发消息时出错:', e);
        }
      }
    }
  });

  // 监听URL变化，重置滚动标记
  let currentUrl = window.location.href;
  const urlObserver = new MutationObserver(() => {
    if (window.location.href !== currentUrl) {
      console.log('URL发生变化，重置滚动标记');
      currentUrl = window.location.href;
      hasStartedScrolling = false;
    }
  });
  
  // 监听DOM变化，检测简历弹窗的打开/关闭
  const domObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 检查是否有简历相关的DOM元素被添加或移除
        const resumeElements = document.querySelectorAll('.resume-detail-wrap, .resume-detail, [class*="resume"]');
        if (resumeElements.length > 0) {
          // 如果发现简历元素，重置滚动标记
          if (hasStartedScrolling) {
            console.log('检测到简历元素变化，重置滚动标记');
            hasStartedScrolling = false;
          }
        }
      }
    });
  });
  
  // 开始观察
  urlObserver.observe(document, { subtree: true, childList: true });
  domObserver.observe(document.body, { childList: true, subtree: true });

  console.log('中继脚本初始化完成');
})(); 