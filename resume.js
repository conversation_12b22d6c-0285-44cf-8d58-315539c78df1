// 简历分析相关的代码
// 全局变量
let analyzedResumeIds = new Set();

// API 配置

// API相关函数
async function getApiEndpoint() {
  const environment = window.resumeManager.currentConfig.apiEnvironment || 'production';
  console.log('当前环境:', environment);
  return window.API_CONFIG[environment].baseUrl;
}

// 数据处理函数
async function loadAnalysisResults() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['analysisResults'], (result) => {
      window.resumeManager.analysisResults = result.analysisResults || [];
      console.log('从存储加载的分析结果:', window.resumeManager.analysisResults);
      updateResultsDisplay();
      resolve(window.resumeManager.analysisResults);
    });
  });
}


// 辅助函数
async function generateShareLink(doc = document) {
  try {
    console.log('开始生成分享链接...');
    
    // 1. 点击分享按钮
    const shareButton = doc.querySelector('.resume-layout-wrap .share i') || 
                       doc.querySelector("div.attachment-resume-top-ui > div.attachment-resume-top-content > div:nth-child(1) > span") ||
                       doc.querySelector("div.icon-coop-forward") ||
                       doc.querySelector("div.pull-left > em.iboss-zhuanfa.icon-coop-forward")||
                       doc.querySelector("div.share.tooltip.tooltip-dark.tooltip-top > i");
    console.log('查找分享按钮:', {
      found: !!shareButton,
      buttonText: shareButton?.textContent,
      buttonClass: shareButton?.className
    });
    
    if (!shareButton) {
      throw new Error('未找到分享按钮');
    }
    
    console.log('找到分享按钮，准备点击');
    shareButton.click();
    
    // 等待分享弹窗出现
    console.log('等待分享弹窗出现...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 查找钉钉转发选项
    let allOptions;
    const firstOptions = document.querySelectorAll('.c-share-box .item-content');
    if (firstOptions && firstOptions.length > 0) {
      allOptions = firstOptions;
    } else {
      try {
        const leftList = document.querySelector("div.boss-popup__content > div > div > div.left-list");
        if (leftList) {
          allOptions = leftList.querySelectorAll(".item");
        } else {
          allOptions = []; // 提供一个空数组作为后备
        }
      } catch (e) {
        console.warn('查找分享选项出错:', e);
        allOptions = []; // 错误处理
      }
    }
    console.log('所有分享选项:', Array.from(allOptions).map(el => el.textContent.trim()));
    
    const dingTalkOption = Array.from(allOptions)
      .find(el => el.textContent.trim() === '钉钉转发');
    
    console.log('钉钉转发选项:', {
      found: !!dingTalkOption,
      text: dingTalkOption?.textContent,
      parentClass: dingTalkOption?.parentElement?.className
    });
    
    if (!dingTalkOption) {
      throw new Error('未找到钉钉转发选项');
    }
    
    console.log('找到钉钉转发选项，准备点击');
    dingTalkOption.click();
    
    // 等待链接生成
    console.log('等待链接生成...');
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 获取分享链接
    const shareInput = document.querySelector("div.boss-popup__content > div > div > div.right-content > div > input");
    console.log('分享链接输入框:', {
      found: !!shareInput,
      value: shareInput?.value,
      type: shareInput?.type
    });
    
    if (!shareInput) {
      throw new Error('未找到分享链接输入框');
    }
    
    const shareLink = shareInput.value;
    console.log('成功获取分享链接:', shareLink);
    
    // 关闭分享弹窗
    const closeButton = document.querySelector('.c-share-box .boss-popup__close');
    console.log('关闭按钮:', {
      found: !!closeButton,
      className: closeButton?.className
    });
    
    if (closeButton) {
      console.log('准备关闭分享弹窗');
      closeButton.click();
    } else {
      console.warn('未找到关闭按钮，但继续执行');
    }
    
    if (!shareLink) {
      throw new Error('分享链接为空');
    }
    
    return shareLink;
    
  } catch (error) {
    console.error('生成分享链接失败:', error);
    console.error('错误堆栈:', error.stack);
    // 打印当前DOM状态
    console.log('当前页面分享相关元素:', {
      shareButton: document.querySelector('.resume-layout-wrap .share i'),
      alternativeShareButton: document.querySelector("div.attachment-resume-top-ui > div.attachment-resume-top-content > div:nth-child(1) > span"),
      shareBox: document.querySelector('.c-share-box'),
      shareInput: document.querySelector("div.boss-popup__content > div > div > div.right-content > div > input")
    });
    return null;
  }
}


// 在文件开头添加初始化代码
window.resumeManager = window.resumeManager || {
  analysisResults: [],
  analyzedResumeIds: new Set(),
  extractSearchPageResumeContent,
  extractChatPageResumeContent,
  extractRecommendPageResumeContent,
  extractCardInnerInfo,
  evaluateResume,
  closeResume,
  favoriteResume,
  isResumeFavorited,
  saveAnalyzedResumeId,
  loadAnalyzedResumeIds,
  getUserScoreThreshold,
  getAutoFavoriteSetting,
  getAutoGreetSetting,
  getAutoShareSetting,
  getUserPrompt,
  getApiKey,
  loadAnalysisResults,
  saveAnalysisResults,
  clickElement,
  sendGreeting,
  // 新增以下函数
  createAnalysisStatusElement,
  updateAnalysisStatus,
  extractNewGeekCardInfo,
  extractCardInnerInfo,
  extractNewRecommendPageResumeContent,
  generateShareLink
}; 

// 添加获取模型设置的函数
function getModel() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['model'], (result) => {
      resolve(result.model || 'deepseek-v3');
    });
  });
}


// 优化后的保存分析结果函数
function saveAnalysisResults() {
  // 使用 Map 提高查找效率
  const uniqueMap = new Map();
  
  // 遍历并只保留必要字段
  for (const result of window.resumeManager.analysisResults) {
    // 确保工作经历数据的完整性
    const workExperiences = Array.isArray(result.workExperiences) ? result.workExperiences : [];
    const latestWork = workExperiences[0] || {};
    
    // 创建精简的结果对象，只保留必要字段
    const simplifiedResult = {
      name: result.name || '未知',
      gender: result.gender || '未知',
      age: result.age || '未知',
      experience: result.experience || '未知',
      education: result.education || '未知',
      jobStatus: result.jobStatus || '未知',
      position: result.position || '未知',
      expectInfo: {
        location: result.expectInfo?.location || '未知',
        position: result.expectInfo?.position || '未知',
        salary: result.expectInfo?.salary || '未知'
      },
      // 完整保留第一份工作经历的所有字段
      workExperiences: [{
        company: latestWork.company || '',
        position: latestWork.position || '',
        department: latestWork.department || '',
        industry: latestWork.industry || '',
        startDate: latestWork.startDate || '',
        endDate: latestWork.endDate || '',
        description: latestWork.description || ''
      }],
      evaluation: {
        score: result.evaluation?.score || 0,
        isQualified: result.evaluation?.score >= (result.scoreThreshold || 80),
        suggestion: result.evaluation?.suggestion || '',
        details: result.evaluation?.details || ''
      },
      prompt_name: result.prompt_name || '默认模板',
      shareLink: result.shareLink || '',
      timestamp: result.timestamp || new Date().toISOString()
    };
    
    // 使用关键字段创建唯一标识
    const fingerprint = [
      simplifiedResult.name,
      simplifiedResult.age,
      simplifiedResult.workExperiences[0].company,
      simplifiedResult.workExperiences[0].position,
      simplifiedResult.education
    ].join('_');
    
    // 如果存在重复项，保留最新的记录
    const existing = uniqueMap.get(fingerprint);
    if (!existing || new Date(simplifiedResult.timestamp) > new Date(existing.timestamp)) {
      uniqueMap.set(fingerprint, simplifiedResult);
    }
  }
  
  // 转换回数组并更新
  const uniqueResults = Array.from(uniqueMap.values());
  window.resumeManager.analysisResults = uniqueResults;
  
  // 异步保存到存储
  return new Promise((resolve) => {
    chrome.storage.local.set({ 
      'analysisResults': uniqueResults 
    }, () => {
      console.log(`已保存 ${uniqueResults.length} 条精简后的分析结果`);
      resolve();
    });
  });
}

// 提取简历内容函数
async function extractSearchPageResumeContent(doc, resumeId) {
  try {
    // 等待简历详情加载完成（最多等待10秒）
    const startTime = Date.now();
    let resumeDetail = null;
    
    while (!resumeDetail && Date.now() - startTime < 10000) {
      // 从主文档中查找简历详情
      try {
        resumeDetail = doc.querySelector('.boss-dialog__wrapper .resume-detail-wrap')
      } catch (e) {
        console.warn('查找简历详情元素时出错:', e);
      }
      
      if (!resumeDetail) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    if (!resumeDetail) {
      console.warn('无法获取简历数据，返回空对象');
      return {
        resumeId,
        name: '',
        gender: '',
        age: null,
        experience: '',
        education: '',
        jobStatus: '',
        expectInfo: { location: '', position: '', salary: '' },
        workExperiences: [],
        projectExperiences: [],
        educationInfo: { school: '', major: '', degree: '', period: '' },
        selfDescription: '',
        timestamp: new Date().toISOString()
      };
    }

    // 提取基本信息
    let baseInfo = {};
    try {
      
        baseInfo = {
          name: (resumeDetail.querySelector('.geek-name')?.textContent?.trim() || 
                resumeDetail.querySelector('.resume-section.geek-base-info-wrap .section-title')?.textContent?.trim() || ''),
          // gender: (resumeDetail.querySelector('.iboss-icon_man') || 
          //         resumeDetail.querySelector('[class^="iboss-icon_man"]')) ? '男' : 
          //         (resumeDetail.querySelector('.iboss-icon_women') || 
          //         resumeDetail.querySelector('[class^="iboss-icon_women"]')) ? '女' : '',
          isElite: !!resumeDetail.querySelector('img.elite'),
          activeStatus: resumeDetail.querySelector('.active-status')?.textContent?.trim() || ''
        };
      
    } catch (e) {
      console.warn('提取基本信息时出错:', e);
      baseInfo = {
        name: '',
        gender: '',
        isElite: false,
        activeStatus: ''
      };
    }

    // 提取标签信息
    let labels = [];
    try {
      
        labels = Array.from(resumeDetail.querySelectorAll('.info-labels span') || [])
          .map(span => span?.textContent?.trim() || '')
      .filter(text => text && !text.includes('line'));
      
    } catch (e) {
      console.warn('提取标签信息时出错:', e);
    }
    
    // 解析标签信息
    let age = '', experience = '', education = '', jobStatus = '';
    try {
      if (labels.length >= 1) age = labels[0];
      if (labels.length >= 2) experience = labels[1];
      if (labels.length >= 3) education = labels[2];
      if (labels.length >= 4) jobStatus = labels[3];
    } catch (error) {
      console.warn('解析标签信息失败:', error);
    }

    // 尝试从新页面结构中提取工作经验
    if (!experience) {
      try {
        
          const positionExperienceTag = resumeDetail.querySelector('.resume-section.geek-position-experience-wrap .tags .tag');
          if (positionExperienceTag) {
            const expText = positionExperienceTag?.textContent || '';
            const expMatch = expText.match(/(\d+)年(\d+)个月/);
            if (expMatch) {
              experience = `${expMatch[1]}年${expMatch[2]}个月`;
            }
          }
        
      } catch (e) {
        console.warn('提取新页面工作经验时出错:', e);
      }
    }

    // 提取期望职位信息
    let expectInfo = { location: '', position: '', salary: '' };
    try {
      
        // 尝试从原有结构中获取
        let originalLocation = '', originalPosition = '', originalSalary = '';
        try {
          originalLocation = resumeDetail.querySelector('.geek-expect-wrap .join-text-wrap span:first-child')?.textContent?.trim() || 
                            resumeDetail.querySelector('div[data-v-60bcabcd].geek-expect-wrap .join-text-wrap span:first-child')?.textContent?.trim() || '';
          originalPosition = resumeDetail.querySelector('.geek-expect-wrap .join-text-wrap span:nth-child(3)')?.textContent?.trim() ||
                            resumeDetail.querySelector('div[data-v-60bcabcd].geek-expect-wrap .join-text-wrap span:nth-child(3)')?.textContent?.trim() || '';
          originalSalary = resumeDetail.querySelector('.geek-expect-wrap .join-text-wrap span:last-child')?.textContent?.trim() ||
                          resumeDetail.querySelector('div[data-v-60bcabcd].geek-expect-wrap .join-text-wrap span:last-child')?.textContent?.trim() || '';
        } catch (e) {
          console.warn('提取原有结构期望职位信息时出错:', e);
        }
        
        // 尝试从新结构中获取
        let newLocation = '', newPosition = '', newSalary = '';
        try {
          const newExpectWrap = resumeDetail.querySelector('.resume-section.geek-expect-wrap .join-text-wrap');
          if (newExpectWrap) {
            const spans = newExpectWrap.querySelectorAll('span') || [];
            if (spans.length >= 1) newLocation = spans[0]?.textContent?.trim() || '';
            if (spans.length >= 2) newPosition = spans[1]?.textContent?.trim() || '';
            if (spans.length >= 4) newSalary = spans[3]?.textContent?.trim() || '';
          }
        } catch (e) {
          console.warn('提取新结构期望职位信息时出错:', e);
        }
        
        expectInfo = {
          location: originalLocation || newLocation || '',
          position: originalPosition || newPosition || '',
          salary: originalSalary || newSalary || ''
        };
      
      } catch (error) {
        console.warn('提取期望职位信息失败:', error);
    }

    // 提取自我描述
    let selfDescription = '';
    try {
      
        selfDescription = resumeDetail.querySelector('.text.selfDescription')?.textContent?.trim() || 
                         resumeDetail.querySelector('.geek-base-info-wrap .geek-desc')?.textContent?.trim() || '';
      
    } catch (e) {
      console.warn('提取自我描述时出错:', e);
    }

    // 提取工作经历
    let workExperiences = [];
    try {
      
        // 尝试从原有结构中获取
        try {
          const workWraps = resumeDetail.querySelectorAll('.geek-work-experience-wrap .work-wrap') || [];
          if (workWraps.length > 0) {
            workExperiences = Array.from(workWraps).map(work => {
              try {
                return {
                  company: work.querySelector('.company-name')?.textContent?.trim() || 
                          work.querySelector('.company-name-wrap .name')?.textContent?.trim() || '',
                  position: work.querySelector('.position')?.textContent?.trim() || 
                           work.querySelector('.company-name-wrap .position span')?.textContent?.trim() || '',
            period: work.querySelector('.period')?.textContent?.trim() || '',
            content: work.querySelector('.item-content')?.textContent?.trim() || '',
                  skills: Array.from(work.querySelectorAll('.tags span, .tags .tag') || [])
                    .map(tag => tag?.textContent?.trim() || '')
                    .filter(Boolean)
                };
              } catch (e) {
                console.warn('提取单个工作经历时出错:', e);
                return {
                  company: '',
                  position: '',
                  period: '',
                  content: '',
                  skills: []
                };
              }
            });
          }
        } catch (e) {
          console.warn('提取原有结构工作经历时出错:', e);
        }

        if (!workExperiences.length) {
          // 尝试从第二种结构中获取
          try {
            let workExperienceDiv = null;
          for(let i = 1; i <= 5; i++) {
              try {
            const div = resumeDetail.querySelector(`div.resume-box > div > div > div > div:nth-child(${i})`);
            if(div && div.querySelector('h3')?.textContent?.trim() === '工作经历') {
              workExperienceDiv = div;
              break;
            }
              } catch (e) {
                console.warn(`查找第${i}个div时出错:`, e);
          }
            }
            
          if(workExperienceDiv) {
              const historyItems = workExperienceDiv.querySelectorAll('.history-list .history-item') || [];
              workExperiences = Array.from(historyItems).map(item => {
                try {
                  return {
                company: item.querySelector('.history-item-title .name span:first-child')?.textContent?.trim() || '',
                position: item.querySelector('.name span:nth-child(3)')?.textContent?.trim() || '',
                period: item.querySelector('.period')?.textContent?.trim() || '',
                content: item.querySelector('.item-text .text')?.textContent?.trim() || '',
                    skills: Array.from(item.querySelectorAll('.tags span') || [])
                      .map(tag => tag?.textContent?.trim() || '')
                      .filter(Boolean)
                  };
                } catch (e) {
                  console.warn('提取单个工作经历(第二种结构)时出错:', e);
                  return {
                    company: '',
                    position: '',
                    period: '',
                    content: '',
                    skills: []
                  };
                }
              });
            }
          } catch (e) {
            console.warn('提取第二种结构工作经历时出错:', e);
          }
        }
      
      } catch (error) {
        console.warn('提取工作经历失败:', error);
      }

    // 提取项目经验
    let projectExperiences = [];
    try {
      
        try {
          let projectExperienceDiv = null;
        for(let i = 1; i <= 5; i++) {
            try {
          const div = resumeDetail.querySelector(`div.resume-box > div > div > div > div:nth-child(${i})`);
          if(div && div.querySelector('h3')?.textContent?.trim() === '项目经验') {
            projectExperienceDiv = div;
            break;
          }
            } catch (e) {
              console.warn(`查找项目经验第${i}个div时出错:`, e);
        }
          }
          
        if(projectExperienceDiv) {
            const historyItems = projectExperienceDiv.querySelectorAll('.history-list .history-item') || [];
            projectExperiences = Array.from(historyItems).map(project => {
              try {
                const nameText = project.querySelector('.name')?.textContent || '';
                const nameParts = nameText.split(/\s*<em.*?vline.*?em>\s*|\s*·\s*/);
                return {
                  name: nameParts[0]?.trim() || '',
                  role: nameParts[1]?.trim() || '',
              period: project.querySelector('.period')?.textContent?.trim() || '',
              content: project.querySelector('.text.project-content')?.textContent?.trim() || ''
                };
              } catch (e) {
                console.warn('提取单个项目经验时出错:', e);
                return {
                  name: '',
                  role: '',
                  period: '',
                  content: ''
                };
              }
            });
          }
        } catch (e) {
          console.warn('提取第一种结构项目经验时出错:', e);
        }

        if (!projectExperiences.length) {
          try {
            const projectWraps = resumeDetail.querySelectorAll('.geek-project-experience-wrap .project-wrap') || [];
            projectExperiences = Array.from(projectWraps).map(project => {
              try {
                return {
              name: project.querySelector('.name span:first-child')?.textContent?.trim() || '',
              role: project.querySelector('.name span:last-child')?.textContent?.trim() || '',
              period: project.querySelector('.period')?.textContent?.trim() || '',
              content: project.querySelector('.item-content')?.textContent?.trim() || ''
                };
              } catch (e) {
                console.warn('提取单个项目经验(第二种结构)时出错:', e);
                return {
                  name: '',
                  role: '',
                  period: '',
                  content: ''
                };
              }
            });
          } catch (e) {
            console.warn('提取第二种结构项目经验时出错:', e);
          }
        }
      
      } catch (error) {
        console.warn('提取项目经验失败:', error);
      }

    // 提取教育经历
    let educationInfo = { school: '', major: '', degree: '', period: '' };
    try {
      
        // 尝试从原有结构中获取
        try {
          educationInfo = {
            school: resumeDetail.querySelector('.school-name')?.textContent?.trim() || '',
            major: resumeDetail.querySelector('.major')?.textContent?.trim() || '',
            degree: resumeDetail.querySelector('.school-name-wrap span:last-child')?.textContent?.trim() || '',
            period: resumeDetail.querySelector('.edu-wrap .period')?.textContent?.trim() || ''
          };
        } catch (e) {
          console.warn('提取原有结构教育经历时出错:', e);
        }

        if (!educationInfo.school || !educationInfo.major) {
          // 尝试从第二种结构中获取
          try {
          const schoolInfo = resumeDetail.querySelector('.school-info');
          if (schoolInfo) {
              const nameText = schoolInfo.querySelector('h4.name b')?.textContent || '';
              const degreeText = schoolInfo.querySelector('h4.name')?.textContent || '';
              educationInfo = {
                school: nameText.trim() || '',
                major: schoolInfo.querySelector('.major')?.textContent?.trim() || '',
                degree: degreeText.split('本科')?.[0]?.trim() || '',
                period: schoolInfo.querySelector('.period')?.textContent?.trim() || ''
              };
            }
          } catch (e) {
            console.warn('提取第二种结构教育经历时出错:', e);
          }
        }
        
        // 尝试从新结构中获取
        if (!educationInfo.school || !educationInfo.major) {
          try {
            const newEduWrap = resumeDetail.querySelector('.resume-section.geek-education-experience-wrap .edu-wrap');
            if (newEduWrap) {
              const schoolNameWrap = newEduWrap.querySelector('.school-name-wrap');
              if (schoolNameWrap) {
                educationInfo = {
                  school: schoolNameWrap.querySelector('.school-name')?.textContent?.trim() || '',
                  major: schoolNameWrap.querySelector('.major')?.textContent?.trim() || '',
                  degree: schoolNameWrap.querySelector('span:nth-child(5)')?.textContent?.trim() || '',
                  period: newEduWrap.querySelector('.period')?.textContent?.trim() || ''
                };
              }
            }
          } catch (e) {
            console.warn('提取新结构教育经历时出错:', e);
          }
        }
      
      } catch (error) {
        console.warn('提取教育经历失败:', error);
    }

    // 尝试解析年龄为数字
    let ageNum = null;
    try {
      if (age) {
        const ageMatch = age.match(/(\d+)/);
        if (ageMatch) {
          ageNum = parseInt(ageMatch[1]);
        }
      }
    } catch (e) {
      console.warn('解析年龄为数字时出错:', e);
    }

    const content = {
      resumeId,
      ...baseInfo,
      age: ageNum,
      experience,
      education,
      jobStatus,
      expectInfo,
      workExperiences,
      projectExperiences,
      educationInfo,
      selfDescription,
      timestamp: new Date().toISOString()
    };
    
    console.log('成功提取简历内容:', content);
    return content;
  } catch (error) {
    console.error('提取简历内容时发生错误:', error);
    // 即使发生错误，也返回一个包含基本信息的对象
    return {
      resumeId,
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: { location: '', position: '', salary: '' },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: { school: '', major: '', degree: '', period: '' },
      selfDescription: '',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

// 在函数前部定义全局变量
let thinkingContent = ''; // 用于累积思考内容
let currentResumeId = null; // 当前正在分析的简历ID

// 添加一个全局计数器
let analyzedResumeCounter = 0;

// 修改resetThinkingContent函数
function resetThinkingContent() {
  // 只保留最近的 N 条记录
  const maxRecords = 5;
  const records = thinkingContent.split('================').slice(-maxRecords);
  thinkingContent = records.join('================');
  
  analyzedResumeCounter++;
  thinkingContent += `\n\n================ 简历 #${analyzedResumeCounter} ================\n\n`;
  currentResumeId = null;
}

// 更新评估简历函数，在开始评估时添加简历标识
async function evaluateResume(resumeContent, doc = document) {
  try {
    console.log('resumeContent', resumeContent);
    const apiKey = await getApiKey();
    const prompt_template = await getUserPrompt();
    const model = await getModel();
    
    // 修改这里：确保从 prompt_template 中正确获取 prompt 和 name
    if (!prompt_template || typeof prompt_template !== 'object') {
      console.error('Invalid prompt_template:', prompt_template);
      throw new Error('无效的提示模板');
    }

    const prompt = prompt_template.content;
    const prompt_name = prompt_template.name;

    console.log('prompt_template', prompt_template);  
    console.log('model', model);
    console.log('prompt content:', prompt); // 添加日志检查 prompt 值
    
    if (!apiKey) {
      throw new Error('API密钥未配置');
    }

    const baseUrl = await getApiEndpoint();
    
    // 获取useCustomEndpoint设置
    const useCustomEndpoint = await new Promise((resolve) => {
      chrome.storage.sync.get(['useCustomEndpoint'], (result) => {
        resolve(result.useCustomEndpoint || false);
      });
    });
    
    // 构建settings对象
    const settings = { 
      prompt, 
      model,
      useCustomEndpoint
    };
    
    
    
    // 根据模型类型决定是否使用流式API
    if (model === 'qwq-plus') {
      // 创建分析状态元素
      // const analysisStatus = createAnalysisStatusElement();
      
      // 重置思考内容和当前简历ID（改为不完全重置）
      resetThinkingContent();
      currentResumeId = resumeContent.resumeId;
      
      // 添加开始分析的提示
      // const resumeName = resumeContent.name || '未知姓名';
      // updateAnalysisStatus(analysisStatus, resumeName, false, false, true);
      
      // 使用流式API处理
      const response = await fetch(`${baseUrl}/api/evaluate-resume-common`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          resume: resumeContent,
          settings: settings
        })
      });

      if (!response.ok) {
        const error = await response.text();
        // updateAnalysisStatus(analysisStatus, `API请求失败: ${error}`, true, true);
        throw new Error(`API请求失败: ${error}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let finalResult = null;
      
      // 读取流
      async function readStream() {
        const { done, value } = await reader.read();
        if (done) {
          // 流结束，处理buffer中可能剩余的数据
          if (buffer.trim()) {
            try {
              // 尝试解析最后的buffer内容
              const jsonResult = JSON.parse(buffer);
              if (jsonResult.type === 'result') {
                finalResult = jsonResult.content;
                // updateAnalysisStatus(analysisStatus, `分析完成，评分: ${finalResult.score}`, true);
              } else if (jsonResult.type === 'error') {
                // updateAnalysisStatus(analysisStatus, `错误: ${jsonResult.content}`, true, true);
                throw new Error(jsonResult.content);
              }
            } catch (e) {
              console.error('解析最后的JSON失败:', e, buffer);
              // 如果最后的buffer不是有效的JSON，但finalResult已经存在，继续处理
              if (!finalResult) {
                // updateAnalysisStatus(analysisStatus, '解析结果失败', true, true);
                throw new Error('解析结果失败');
              }
            }
          }
          
          if (!finalResult) {
            // updateAnalysisStatus(analysisStatus, '未收到有效结果', true, true);
            throw new Error('未收到有效结果');
          }

          const shouldAutoShare = await window.resumeManager.getAutoShareSetting();
          const scoreThreshold = await window.resumeManager.getUserScoreThreshold();

          let shareLink = null;
          if (shouldAutoShare && finalResult.score >= scoreThreshold) {
            console.log('执行自动生成分享链接');
            shareLink = await window.resumeManager.generateShareLink(document);
            console.log('分享链接:', shareLink);
          }
          
          // 构建评估结果并保存 - 添加思考过程
          const evaluation = {
            score: finalResult.score,
            details: finalResult.details || {
              baseScore: finalResult.score,
              additions: [],
              totalScore: finalResult.score
            },
            comments: finalResult.comments || '',
            suggestion: finalResult.suggestion || '',
            thinkingProcess: thinkingContent || '' // 保存思考过程
          };

          
          // 将结果添加到分析结果数组
          window.resumeManager.analysisResults.push({
            ...resumeContent,
            evaluation,
            shareLink,
            timestamp: new Date().toISOString(),
            position: {
              resumeId: resumeContent.resumeId
            },
            prompt_name: prompt_name
          });
          
          console.log('新增分析结果:', {
            name: resumeContent.name,
            score: finalResult.score,
            timestamp: new Date().toISOString()
          });
          
          // 保存结果并更新显示
          await saveAnalysisResults();
          updateResultsDisplay();
          
          return finalResult.score;
        }
        
        // 处理接收到的数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        // 按行分割并处理每一行
        const lines = buffer.split('\n');
        // 保留最后一行（可能不完整）
        buffer = lines.pop() || '';
        
        // 处理完整的行
        for (const line of lines) {
          if (!line.trim()) continue;
          
          try {
            // 需要移除这个检查，因为服务器可能没有以 data: 前缀发送数据
            // if (!line.startsWith('data:')) continue;
            
            let content = line;
            // 如果有 data: 前缀，则去掉
            if (line.startsWith('data:')) {
              content = line.substring(5).trim();
            }
            
            // 尝试解析JSON
            try {
              const jsonData = JSON.parse(content);
              
              if (jsonData.type === 'thinking') {
                // 处理思考过程
                // updateAnalysisStatus(analysisStatus, jsonData.content);
              } 
              else if (jsonData.type === 'result') {
                // 保存最终结果
                finalResult = jsonData.content;
                // updateAnalysisStatus(analysisStatus, `分析完成，评分: ${finalResult.score}`);
              }
              else if (jsonData.type === 'error') {
                // 处理错误
                // updateAnalysisStatus(analysisStatus, `错误: ${jsonResult.content}`, false, true);
              }
              else {
                // 处理未知类型的消息
                console.log('收到未知类型的消息:', jsonData);
                // updateAnalysisStatus(analysisStatus, `消息: ${JSON.stringify(jsonData)}`);
              }
            } catch (e) {
              // 如果不是有效的JSON，当作普通文本处理
              console.log('非JSON格式的数据:', content);
              // updateAnalysisStatus(analysisStatus, content);
            }
          } catch (e) {
            console.warn('处理流数据行时出错:', e);
          }
        }
        
        // 继续读取
        return readStream();
      }
      
      return await readStream();
    } else {
      // 原有的非流式API处理逻辑
 
      // 创建一个超时Promise，使用90秒作为最大超时时间
      const timeout = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('API请求超时')), 90000);
      });

      console.log('no qwq prompt', prompt); // 使用 prompt 变量
  
      // 创建API请求Promise
      const fetchPromise = fetch(`${baseUrl}/api/evaluate-resume-common`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          resume: resumeContent,
          settings: settings
        })
      });
  
      // 使用Promise.race来处理超时
      let response;
      try {
        response = await Promise.race([fetchPromise, timeout]);
      } catch (error) {
        // 如果是超时或其他错误，等待随机时间后抛出
        await new Promise(resolve => setTimeout(resolve, getRandomDelay()));
        
        const errorMessage = error.message === 'API请求超时' 
          ? '评估请求超时，请稍后重试'
          : '网络连接错误，请检查网络设置';
        
        showError(errorMessage);
        throw new Error(errorMessage);
      }
 
      if (!response.ok) {
        // API调用失败，等待随机时间后抛出
        await new Promise(resolve => setTimeout(resolve, getRandomDelay()));
        
        if (response.status === 403) {
          console.error('API密钥验证失败：403错误');
          showError('API调用次数已用完，请充值后继续使用');
          stopAnalysis();
          throw new Error('API_QUOTA_EXCEEDED');
        }
        
        let errorMessage = '';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || `API请求失败: ${response.status}`;
        } catch (e) {
          errorMessage = '解析错误响应失败';
        }
        
        showError(errorMessage);
        throw new Error(errorMessage);
      }
 
      const result = await response.json();
      
      // 计算总分：baseScore + 所有加分项的分数总和
      const totalScore = result.details.baseScore + 
        result.details.additions.reduce((sum, item) => sum + item.score, 0);

      let shareLink = null;
      const shouldAutoShare = await window.resumeManager.getAutoShareSetting();
      const scoreThreshold = await window.resumeManager.getUserScoreThreshold();
      
      if (shouldAutoShare && totalScore >= scoreThreshold) {
        console.log('执行自动生成分享链接');
        shareLink = await window.resumeManager.generateShareLink(document);
        console.log('分享链接:', shareLink);
      }
      
      // 构建评估结果
      const evaluation = {
        score: totalScore, // 使用计算出的总分
        details: {
          ...result.details,
          totalScore // 添加计算出的总分到详情中
        },
        comments: result.details.additions.map(item => `${item.rule}: ${item.reason}`).join('\n'),
        suggestion: result.suggestion
        
      };
 
      console.log('评分详情:', {
        baseScore: result.details.baseScore,
        additionsScores: result.details.additions.map(item => item.score),
        totalScore
      });
 
      // 将结果添加到分析结果数组
      window.resumeManager.analysisResults.push({
        ...resumeContent,
        evaluation,
        shareLink,
        timestamp: new Date().toISOString(),
        position: {
          resumeId: resumeContent.resumeId
        },
        prompt_name: prompt_name
      });
 
      console.log('新增分析结果:', {
        name: resumeContent.name,
        score: totalScore, // 使用计算出的总分
        timestamp: new Date().toISOString()
      });
 
      // 保存结果并更新显示
      await saveAnalysisResults();
      updateResultsDisplay();
 
      return totalScore; // 返回计算出的总分
    }
  } catch (error) {
    console.error('评估简历时发生错误:', error);
    throw error;
  }
}

// 添加getRandomDelay函数，用于生成随机延迟时间
function getRandomDelay(min = 1000, max = 3000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 创建分析状态显示元素
function createAnalysisStatusElement() {
  // 检查是否已存在
  let statusElement = document.getElementById('ai-analysis-status');
  if (statusElement) {
    // 不清空内容，保留现有内容
    return statusElement;
  }
  
  // 创建新的状态元素
  statusElement = document.createElement('div');
  statusElement.id = 'ai-analysis-status';
  statusElement.className = 'ai-analysis-status';
  
  // 设置样式 - 增强可见度
  statusElement.style.position = 'fixed';
  statusElement.style.bottom = '20px';
  statusElement.style.left = '20px'; // 从right改为left
  statusElement.style.maxWidth = '400px'; 
  statusElement.style.minWidth = '300px';
  statusElement.style.padding = '15px';
  statusElement.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
  statusElement.style.color = '#fff';
  statusElement.style.borderRadius = '8px';
  statusElement.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
  statusElement.style.zIndex = '10000';
  statusElement.style.fontSize = '13px';
  statusElement.style.lineHeight = '1.5';
  statusElement.style.maxHeight = '300px';
  statusElement.style.overflowY = 'auto';
  statusElement.style.transition = 'all 0.3s ease';
  statusElement.style.backdropFilter = 'blur(5px)';
  statusElement.style.border = '1px solid rgba(255, 255, 255, 0.1)';
  
  // 添加标题栏，包含标题和关闭按钮
  const header = document.createElement('div');
  header.style.display = 'flex';
  header.style.justifyContent = 'space-between';
  header.style.alignItems = 'center';
  header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.2)';
  header.style.marginBottom = '10px';
  header.style.paddingBottom = '5px';
  
  // 添加标题
  const title = document.createElement('span');
  title.style.fontWeight = 'bold';
  title.textContent = 'AI 分析思考过程';
  header.appendChild(title);
  
  // 添加清空按钮
  const clearButton = document.createElement('button');
  clearButton.innerHTML = '清空';
  clearButton.style.background = 'none';
  clearButton.style.border = 'none';
  clearButton.style.color = '#fff';
  clearButton.style.fontSize = '12px';
  clearButton.style.cursor = 'pointer';
  clearButton.style.padding = '0 5px';
  clearButton.style.marginRight = '5px';
  clearButton.title = '清空所有思考记录';
  
  // 添加清空按钮的事件处理
  clearButton.addEventListener('click', function() {
    thinkingContent = '';
    const thinkingContainer = statusElement.querySelector('#thinking-container');
    if (thinkingContainer) {
      thinkingContainer.innerHTML = '';
    }
  });
  
  header.appendChild(clearButton);
  
  // 添加关闭按钮
  const closeButton = document.createElement('button');
  closeButton.innerHTML = '×';
  closeButton.style.background = 'none';
  closeButton.style.border = 'none';
  closeButton.style.color = '#fff';
  closeButton.style.fontSize = '18px';
  closeButton.style.cursor = 'pointer';
  closeButton.style.padding = '0 5px';
  closeButton.style.lineHeight = '1';
  closeButton.title = '关闭思考过程窗口';
  
  // 添加关闭按钮的事件处理
  closeButton.addEventListener('click', function() {
    if (statusElement.parentNode) {
      statusElement.parentNode.removeChild(statusElement);
    }
  });
  
  header.appendChild(closeButton);
  statusElement.appendChild(header);
  
  // 添加思考过程的容器
  const thinkingContainer = document.createElement('div');
  thinkingContainer.id = 'thinking-container';
  thinkingContainer.className = 'thinking-container';
  thinkingContainer.style.marginBottom = '10px';
  statusElement.appendChild(thinkingContainer);
  
  // 添加结果容器
  const resultContainer = document.createElement('div');
  resultContainer.id = 'result-container';
  resultContainer.className = 'result-container';
  statusElement.appendChild(resultContainer);
  
  document.body.appendChild(statusElement);
  
  // 添加拖动和最小化功能
  enhanceAnalysisStatusElement(statusElement);
  
  return statusElement;
}

// 添加拖动和最小化功能的初始设置
function enhanceAnalysisStatusElement(statusElement) {
  if (!statusElement) return;
  
  // 添加可拖动功能
  let isDragging = false;
  let dragOffsetX, dragOffsetY;
  
  // 获取标题栏作为拖动区域
  const header = statusElement.querySelector('div:first-child');
  if (!header) return;
  
  // 添加拖动提示
  header.style.cursor = 'move';
  header.title = '按住拖动';
  
  // 添加最小化/最大化按钮
  const toggleButton = document.createElement('button');
  toggleButton.innerHTML = '−';
  toggleButton.style.background = 'none';
  toggleButton.style.border = 'none';
  toggleButton.style.color = '#fff';
  toggleButton.style.fontSize = '18px';
  toggleButton.style.cursor = 'pointer';
  toggleButton.style.padding = '0 5px';
  toggleButton.style.marginRight = '5px';
  toggleButton.style.lineHeight = '1';
  toggleButton.title = '最小化';
  
  // 插入到标题栏，在标题和关闭按钮之间
  const closeButton = header.querySelector('button');
  if (closeButton) {
    header.insertBefore(toggleButton, closeButton);
  } else {
    header.appendChild(toggleButton);
  }
  
  // 最小化/最大化切换功能
  let isMinimized = false;
  const thinkingContainer = statusElement.querySelector('#thinking-container');
  const resultContainer = statusElement.querySelector('#result-container');
  
  toggleButton.addEventListener('click', function() {
    if (isMinimized) {
      // 最大化
      if (thinkingContainer) thinkingContainer.style.display = 'block';
      if (resultContainer) resultContainer.style.display = 'block';
      statusElement.style.maxHeight = '300px';
      toggleButton.innerHTML = '−';
      toggleButton.title = '最小化';
      isMinimized = false;
    } else {
      // 最小化
      if (thinkingContainer) thinkingContainer.style.display = 'none';
      if (resultContainer) resultContainer.style.display = 'none';
      statusElement.style.maxHeight = 'auto';
      statusElement.style.height = 'auto';
      toggleButton.innerHTML = '+';
      toggleButton.title = '最大化';
      isMinimized = true;
    }
  });
  
  // 拖动开始
  header.addEventListener('mousedown', function(e) {
    isDragging = true;
    dragOffsetX = e.clientX - statusElement.offsetLeft;
    dragOffsetY = e.clientY - statusElement.offsetTop;
    e.preventDefault();
  });
  
  // 拖动过程
  document.addEventListener('mousemove', function(e) {
    if (!isDragging) return;
    
    // 计算新位置
    const newLeft = e.clientX - dragOffsetX;
    const newTop = e.clientY - dragOffsetY;
    
    // 确保不超出屏幕
    const maxLeft = window.innerWidth - statusElement.offsetWidth;
    const maxTop = window.innerHeight - statusElement.offsetHeight;
    
    statusElement.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
    statusElement.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
    statusElement.style.bottom = 'auto'; // 取消底部定位
  });
  
  // 拖动结束
  document.addEventListener('mouseup', function() {
    isDragging = false;
  });
  
  return statusElement;
}

// 更新分析状态函数，不再完全重置内容
function updateAnalysisStatus(element, message, isComplete = false, isError = false, isNewResume = false) {
  if (!element) return;
  
  const thinkingContainer = element.querySelector('#thinking-container');
  if (thinkingContainer) {
    // 使用 DocumentFragment 减少 DOM 操作
    const fragment = document.createDocumentFragment();
    const thinkingElement = document.createElement('pre');
    thinkingElement.className = 'status-thinking typing-content';
    thinkingElement.style.whiteSpace = 'pre-wrap';
    thinkingElement.textContent = message;
    fragment.appendChild(thinkingElement);
    
    // 一次性更新 DOM
    thinkingContainer.appendChild(fragment);
    
    // 限制容器中的子元素数量
    while (thinkingContainer.children.length > 100) {
      thinkingContainer.removeChild(thinkingContainer.firstChild);
    }
  }
}

// 显示错误提示
function showError(message) {
  const errorContainer = document.getElementById('errorContainer');
  const errorMessage = document.getElementById('errorMessage');
  
  if (errorContainer && errorMessage) {
    errorMessage.textContent = message;
    errorContainer.classList.add('show');
    
    // 5秒后自动隐藏
    setTimeout(() => {
      errorContainer.classList.remove('show');
    }, 5000);
  } else {
    console.error('错误提示容器未找到，错误信息：', message);
  }
}

// 简历操作相关函数
async function closeResume(doc = document) {
  console.log('关闭简历对话框');
  const closeButton = doc.querySelector('.boss-dialog__wrapper.dialog-lib-resume .dialog-close') || 
                      doc.querySelector(".boss-dialog__wrapper.resume-common-dialog.search-resume.new-chat-resume-dialog-main-ui.resume-container > div.boss-popup__close") ||
                      doc.querySelector(".boss-dialog__wrapper .dialog-close") ||
                      doc.querySelector("div.boss-popup__close") ||
                      doc.querySelector(".dialog-container .dialog-close")
                     
  if (closeButton) {
    closeButton.click();
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

async function favoriteResume(doc = document) {
  try {
    console.log('尝试收藏简历');
    // 首先检查是否已收藏
    const isFavorited = await isResumeFavorited(doc);
    if (isFavorited) {
      console.log('简历已收藏，跳过收藏');
      return true;
    }
    
    // 查找收藏按钮
    const favoriteButton = doc.querySelector('.boss-dialog__wrapper.dialog-lib-resume .favorite-btn') ||
                          doc.querySelector("div.attachment-resume-top-ui > div.attachment-resume-top-content > div:nth-child(2) > span") ||
                          doc.querySelector(".boss-dialog__wrapper .favorite-btn") ||
                          doc.querySelector("div.pull-left > div.like-icon-and-text > div.icon-like > div") ||
                          doc.querySelector("div.pull-left > div > div.like-icon") ||
                          doc.querySelector("div.boss-popup__content > div > div > div.resume-layout-wrap > div.resume-middle-wrap > div > div > div.resume-section.geek-base-info-wrap > div.section-content > div.name-flex > div.operate > div.interested.tooltip.tooltip-dark.tooltip-top > i");
    
    // 如果找到按钮且未被激活，点击它
    if (favoriteButton && !favoriteButton.classList.contains('active')) {
      favoriteButton.click();
      console.log('已点击收藏按钮');
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    } else if (!favoriteButton) {
      console.log('未找到收藏按钮，收藏失败');
      return false;
    } else {
      console.log('收藏按钮已激活，无需再次点击');
      return true;
    }
  } catch (error) {
    console.error('收藏简历时发生错误:', error);
    // 返回false但不抛出异常，这样不会中断整个分析流程
    return false;
  }
}

async function sendGreeting(doc = document) {
  console.log('尝试发送打招呼消息');
  try {
    // 查找打招呼按钮 (添加了新的选择器)
    const greetButton = 
      // 推荐页面打招呼
      doc.querySelector("div.communication.icon-coop-forward.shareReport > div > div > div.button-list-wrap > div > span > div > button") ||
      // 搜索页面打招呼
      doc.querySelector("div.resume-footer-wrap > div > div.btns > div > button") ||
      // 其他原来的打招呼逻辑
      doc.querySelector('.boss-dialog__wrapper.dialog-lib-resume .greet-btn') ||
      doc.querySelector('.attachment-resume-top-ui .greet-btn') ||
      doc.querySelector('.boss-dialog__wrapper .greet-btn') ||
      doc.querySelector("div.button-list-wrap > div > span > div > button") || // 原有结构选择器
      doc.querySelector('.button-list-wrap .btn-greet');
                        

    if (greetButton) {
      // 确保按钮可见且可点击 (可选，增加健壮性)
      if (!(greetButton instanceof HTMLElement)) {
         console.log('打招呼按钮不是一个有效的HTML元素，跳过处理');
         return false;
      }

      const styles = window.getComputedStyle(greetButton);
      const isEssentiallyVisible = styles.display !== 'none' &&
                                 styles.visibility !== 'hidden' &&
                                 parseFloat(styles.opacity) > 0 &&
                                 greetButton.offsetWidth > 0 &&
                                 greetButton.offsetHeight > 0;

      // 检查 offsetParent 和计算后的可见性
      if (greetButton.offsetParent === null || !isEssentiallyVisible) {
         console.log('打招呼按钮虽然找到，但判定为不可见或不可交互，跳过。详细原因:', {
            isHTMLElement: greetButton instanceof HTMLElement,
            offsetParentIsNull: greetButton.offsetParent === null,
            computedDisplay: styles.display,
            computedVisibility: styles.visibility,
            computedOpacity: styles.opacity,
            offsetWidth: greetButton.offsetWidth,
            offsetHeight: greetButton.offsetHeight,
            outerHTMLSnippet: greetButton.outerHTML ? greetButton.outerHTML.substring(0, 250) : 'N/A'
         });
         return false;
      }

      greetButton.click();
      console.log('已点击打招呼按钮');
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待弹窗出现

      // 查找发送按钮并点击 (这部分选择器可能也需要根据实际情况调整)
      const sendButton = doc.querySelector('.dialog-chat-quick-reply .send-btn') ||
                         doc.querySelector('.dialog-chat-quick-reply button[type="submit"]') ||
                         doc.querySelector('.boss-dialog__wrapper .send-btn') ||
                         doc.querySelector('.boss-dialog__wrapper button[type="submit"]') ||
                         doc.querySelector('.greet-dialog-wrap .btn-sure'); // 尝试添加可能的发送按钮选择器

      if (sendButton) {
        sendButton.click();
        console.log('已发送打招呼消息');
        await new Promise(resolve => setTimeout(resolve, 500));
        return true;
      } else {
        console.log('未找到发送按钮（或弹窗结构不同）');
        // 尝试关闭可能的弹窗
        const closeButton = doc.querySelector('.greet-dialog-wrap .icon-close') || doc.querySelector('.boss-dialog__wrapper .dialog-close');
        if (closeButton) {
            closeButton.click();
            console.log('尝试关闭打招呼弹窗');
        }
        return false;
      }
    } else {
      console.log('未找到打招呼按钮');
      return false;
    }
  } catch (error) {
    console.error('发送打招呼消息时出错:', error);
    return false;
  }
}

// 已分析简历ID管理
async function saveAnalyzedResumeId(resumeId) {
  console.log('保存已分析的简历ID:', resumeId);
  analyzedResumeIds.add(resumeId);
  
  // 更新 analysisResults 中对应结果的 resumeId（以防万一）
  const latestResult = window.resumeManager.analysisResults[window.resumeManager.analysisResults.length - 1];
  if (latestResult && !latestResult.resumeId) {
    latestResult.resumeId = resumeId;
  }
  
  await Promise.all([
    // 保存已分析的ID集合
    new Promise(resolve => {
      chrome.storage.local.set({
        'analyzedResumeIds': Array.from(analyzedResumeIds)
      }, resolve);
    }),
    // 保存更新后的分析结果
    saveAnalysisResults()
  ]);
}

async function loadAnalyzedResumeIds() {
  console.log('加载已分析的简历ID');
  return new Promise(resolve => {
    chrome.storage.local.get(['analyzedResumeIds'], (result) => {
      if (result.analyzedResumeIds) {
        analyzedResumeIds = new Set(result.analyzedResumeIds);
      }
      resolve();
    });
  });
}

// 用户设置相关函数
function getUserScoreThreshold() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['scoreThreshold'], (result) => {
      resolve(result.scoreThreshold || 80);
    });
  });
}

function getAutoFavoriteSetting() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['autoFavorite'], (result) => {
      // 默认为true，即默认自动收藏
      resolve(result.autoFavorite !== undefined ? result.autoFavorite : true);
    });
  });
}

function getAutoGreetSetting() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['autoGreet'], (result) => {
      // 默认为false，即默认不自动打招呼
      resolve(result.autoGreet !== undefined ? result.autoGreet : false);
    });
  });
}

function getAutoShareSetting() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['autoShare'], (result) => {
      resolve(result.autoShare !== undefined ? result.autoShare : false);
    });
  });
}

// 获取用户设置的Prompt
function getUserPrompt() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['promptTemplates', 'selectedPromptIndex'], (result) => {
      console.log('获取到的 promptTemplates:', result.promptTemplates);
      console.log('获取到的 selectedPromptIndex:', result.selectedPromptIndex);
      
      const templates = result.promptTemplates || [{
        name: '默认模板',
        content: `请根据以下标准评估这份简历（默认0分）：

基础分（60分）：
1. 工作经历中有ToB公司经验（+20分）
2. 简历中提到AI相关技能或经验（+10分）
3. 有独立文案、视频剪辑制作能力或相关工具使用经验（+10分）
4. 有线上、线下活动运营经验（+10分）
5. 有大型互联网公司工作经验（+10分）

加分项（最多40分）：
1. 学历为本科及以上（+15分）
2. 最近5年工作稳定性：
   - 3家以内公司（+10分）
   - 3-5家公司（+5分）
   - 5家以上公司（-5分）
3. 有相关行业经验（+15分）：
   - 网络安全/信息安全行业
   - 企业服务行业
   - 人工智能行业

请详细说明每项得分原因，并给出改进建议。`
      }];
      const selectedIndex = result.selectedPromptIndex || 0;
      
      // 添加日志
      console.log('选择的模板:', templates[selectedIndex]);
      
      resolve(templates[selectedIndex]);
    });
  });
}

function getApiKey() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['apiKey'], (result) => {
      resolve(result.apiKey || '');
    });
  });
}


// 添加等待元素出现的辅助函数
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    if (document.querySelector(selector)) {
      return resolve(document.querySelector(selector));
    }

    const observer = new MutationObserver((mutations) => {
      if (document.querySelector(selector)) {
        observer.disconnect();
        resolve(document.querySelector(selector));
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`等待元素 ${selector} 超时`));
    }, timeout);
  });
}

async function retry(fn, maxAttempts = 3, delay = 1000) {
  let lastError;
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  throw lastError;
}

async function clickElement(element, view) {
  if (element && typeof element.click === 'function') {
    element.click();
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// 添加检查简历是否已收藏的函数
async function isResumeFavorited(doc = document) {
  console.log('检查简历是否已收藏');
  const favoriteButton = doc.querySelector('.boss-dialog__wrapper.dialog-lib-resume .favorite-btn') ||
                        doc.querySelector("div.attachment-resume-top-ui > div.attachment-resume-top-content > div:nth-child(2) > span") ||
                        doc.querySelector(".boss-dialog__wrapper .favorite-btn") ||
                        doc.querySelector("div.communication.icon-coop-forward.shareReport > div > div > div.pull-left > div > div.like-text") || //新牛人
                        doc.querySelector("div.communication.icon-coop-forward.shareReport > div > div > div.pull-left > div.like-icon-and-text > div.btn-text") ||// 推荐牛人
                        doc.querySelector("div.resume-section.geek-base-info-wrap > div.section-content > div.name-flex > div.operate > div.interested.tooltip.tooltip-dark.tooltip-top.already-interested") // 搜索牛人
  
  if (!favoriteButton) {
    console.log('未找到收藏按钮');
    return false;
  }

  // 检查是否已收藏（通过按钮的active类或其他标识），添加null检查
  const isFavorited = favoriteButton.classList.contains('active') || 
                     favoriteButton.classList.contains('favorite-btn-active') ||
                     (favoriteButton.textContent && favoriteButton.textContent.includes('已收藏')) ||
                     (favoriteButton.ariaLabel && favoriteButton.ariaLabel.includes('取消收藏'));
  
  console.log('简历收藏状态:', isFavorited);
  return isFavorited;
}

// 处理搜索页面的简历分析
async function handleSearchPageAnalysis(button) {
  console.log('开始处理搜索页面的简历分析');
  
  const searchFrame = document.querySelector('iframe[name="searchFrame"]');
  if (!searchFrame) {
    showError('找不到搜索iframe，无法开始分析。');
    throw new Error('找不到搜索 iframe');
  }
  const iframeDoc = searchFrame.contentDocument || searchFrame.contentWindow.document;
  console.log('已获取 iframe 文档');

  await waitForElementInIframe(iframeDoc, '#is-gray-batch-chat .card-list');
  console.log('简历列表已加载');
  
  await closeAllResumeDialogs(document);
  
  const processedIds = new Set();
  let previousCardCount = 0;
  let sameCardCountTimes = 0;
  let dialogTimeoutCheck;

  let consecutiveScrollsWithoutNewProcessing = 0; // 新增：连续滚动但未处理新简历的计数器
  const MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING = 5; // 新增：最大允许的连续空滚动次数

  while (isAnalyzing) {
    const resumeCards = iframeDoc.querySelectorAll('#is-gray-batch-chat .card-list > li');
    console.log(`当前视口发现 ${resumeCards.length} 个简历卡片。`);

    if (resumeCards.length === 0 && previousCardCount === 0) {
        console.log('未发现任何简历卡片，可能页面结构已更改或加载失败。');
        showError('在搜索页面未找到简历卡片，请检查页面是否正确加载。');
        isAnalyzing = false;
        break;
    }

    if (resumeCards.length === previousCardCount) {
      sameCardCountTimes++;
      if (sameCardCountTimes >= 3) {
        console.log(`连续 ${sameCardCountTimes} 次获取到相同数量的卡片 (${resumeCards.length}) 且无新简历处理，可能已到达底部或加载异常，终止分析。`);
        isAnalyzing = false; // 确保停止
        break;
      }
    } else {
      previousCardCount = resumeCards.length;
      sameCardCountTimes = 0;
    }
    
    let newResumeActuallyProcessedInThisPass = false; // 用于判断本轮是否有简历被 *实际处理*
    let allCurrentCardsSkippedOrProcessed = true; // 假设当前所有卡片都会被跳过或已处理

    for (const card of resumeCards) {
      if (!isAnalyzing) {
        console.log('分析过程被中断 (循环内部)。');
        return;
      }

      const resumeId = card.getAttribute('data-expect') || 
                      card.querySelector('a[ka="search_click_open_resume"]')?.getAttribute('data-expect');
      
      if (!resumeId) {
        console.warn('无法获取简历ID，跳过此卡片。卡片HTML片段:', card.outerHTML ? card.outerHTML.substring(0, 300) : "N/A");
        continue;
      }
      
      if (processedIds.has(resumeId)) {
        // console.log(`跳过当前会话已处理的简历 ${resumeId}`); // 此日志级别可能过高，可按需开启
        continue;
      }
      
      const isAnalyzed = await checkResumeAnalyzed(resumeId);
      if (isAnalyzed) {
        console.log(`跳过历史记录中已分析的简历 ${resumeId} (来自 checkResumeAnalyzed)`);
        processedIds.add(resumeId); // 也添加到当前会话，避免重复检查
        continue;
      }

      // 如果执行到这里，说明这个简历是新的，且之前未被处理过
      allCurrentCardsSkippedOrProcessed = false; // 发现一个需要处理的卡片
      processedIds.add(resumeId); // 先标记为本轮尝试处理，避免因异步导致重复打开

      try {
        console.log(`准备处理新简历 ${resumeId}...`);
        
        await closeAllResumeDialogs(document);
        
        const clickableLink = card.querySelector('a[ka="search_click_open_resume"]');
        if (clickableLink) {
          if (!isAnalyzing) return;
          
          // 添加更详细的调试信息
          console.log(`准备点击简历 ${resumeId} 的链接，链接元素:`, clickableLink);
          console.log(`链接是否可见:`, clickableLink.offsetParent !== null);
          console.log(`链接是否可点击:`, !clickableLink.disabled && clickableLink.style.pointerEvents !== 'none');
          
          await window.resumeManager.clickElement(clickableLink, searchFrame.contentWindow);
          console.log(`已点击简历 ${resumeId} 的链接`);
          
          // 添加点击后的等待时间，确保页面响应
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          try {
            // 增加等待时间，并添加更详细的调试信息
            console.log(`开始等待简历 ${resumeId} 对话框加载...`);
            await waitForDialog(document, 15000); // 增加超时时间到15秒
            console.log(`简历 ${resumeId} 对话框已加载`);
          } catch (error) {
            if (error.message === '用户中断操作') return;
            console.error(`等待简历 ${resumeId} 对话框加载失败:`, error);
            
            // 添加更多调试信息
            console.log('当前页面状态检查:');
            const existingDialogs = document.querySelectorAll('.boss-dialog__wrapper, .resume-detail-wrap, .dialog-container');
            console.log(`现有对话框数量: ${existingDialogs.length}`);
            existingDialogs.forEach((dialog, index) => {
              console.log(`对话框 ${index + 1}:`, dialog.className, dialog.style.display);
            });
            
            // 尝试强制关闭可能存在的弹窗
            await window.resumeManager.closeResume(document);
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待更长时间
            
            continue; // 跳过此简历的处理
          }
          
          if (!isAnalyzing) return;
          
          const resumeContent = await window.resumeManager.extractSearchPageResumeContent(document, resumeId);
          console.log(`已提取简历 ${resumeId} 的内容`);
          if (!isAnalyzing) return;

          try {
            console.log(`开始AI评估简历 ${resumeId}`);
            const score = await window.resumeManager.evaluateResume(resumeContent, document);
            console.log(`简历 ${resumeId} 评估完成，分数:`, score);
            newResumeActuallyProcessedInThisPass = true; // 标记有简历被成功评估
            consecutiveScrollsWithoutNewProcessing = 0; // 重置连续空滚动计数器

            if (!isAnalyzing) return;
            
            const threshold = await window.resumeManager.getUserScoreThreshold();
            if (score >= threshold) {
              console.log(`简历 ${resumeId} 分数 (${score}) 达标 (阈值 ${threshold})`);
              if (!isAnalyzing) return;
              
              const shouldAutoFavorite = await window.resumeManager.getAutoFavoriteSetting();
              if (shouldAutoFavorite) {
                console.log(`执行自动收藏简历 ${resumeId}`);
                await window.resumeManager.favoriteResume(document);
              }
              
              const shouldAutoGreet = await window.resumeManager.getAutoGreetSetting();
              if (shouldAutoGreet) {
                console.log(`执行自动打招呼 (简历 ${resumeId})`);
                await window.resumeManager.sendGreeting(iframeDoc); // 注意: sendGreeting 现在在iframeDoc上操作
              }
            }
            
            await window.resumeManager.saveAnalyzedResumeId(resumeId);
            console.log(`已保存简历 ${resumeId} 的分析记录`);
            
          } catch (evalError) {
            console.error(`简历 ${resumeId} AI评估失败:`, evalError);
            // 评估失败也算尝试处理过，但不重置 consecutiveScrollsWithoutNewProcessing，除非评估是主要瓶颈
          }
          
          await new Promise(resolve => setTimeout(resolve, isAnalyzing ? 5000 : 100)); // 缩短停止时的等待
          if (!isAnalyzing) return;
          
          await window.resumeManager.closeResume(document);
          console.log(`已关闭简历 ${resumeId} 的弹窗`);
          
          // 增加关闭后的等待时间，确保页面状态完全重置
          await new Promise(resolve => setTimeout(resolve, isAnalyzing ? 3000 : 100));
        }
      } catch (error) {
        console.error(`处理简历 ${resumeId} 时发生主错误:`, error);
        if (isAnalyzing) {
          await window.resumeManager.closeResume(document); // 确保关闭
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      if (!isAnalyzing) return;
    } // 结束 for (const card of resumeCards)

    // 如果在当前视口的卡片中，所有卡片都被跳过了（即没有一个是新的、未分析的）
    if (allCurrentCardsSkippedOrProcessed && resumeCards.length > 0) {
      console.log(`当前视口所有 ${resumeCards.length} 个简历均被跳过 (已分析或ID无效)。`);
      consecutiveScrollsWithoutNewProcessing++;
      console.log(`连续空滚动次数: ${consecutiveScrollsWithoutNewProcessing}/${MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING}`);

      if (consecutiveScrollsWithoutNewProcessing >= MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING) {
        console.error(`已连续 ${MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING} 次滚动后未找到可处理的新简历。分析可能因ID问题或历史记录问题卡住。将停止分析。`);
        showError(`分析似乎卡住（连续 ${MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING} 次滚动后未处理新简历）。已自动停止。请检查控制台日志。`);
        isAnalyzing = false;
        break; 
      }

      // 准备滚动加载更多
      console.log('准备滚动加载更多简历...');
      await autoScroll(iframeDoc);
      await new Promise(resolve => setTimeout(resolve, 3000)); 
      
      const newCardsAfterScroll = iframeDoc.querySelectorAll('#is-gray-batch-chat .card-list > li');
      if (newCardsAfterScroll.length === resumeCards.length && !newResumeActuallyProcessedInThisPass) {
         // 检查 previousCardCount 而不是 resumeCards.length，因为 resumeCards.length 是滚动前的数量
        console.log('滚动后卡片数量未增加，且本轮未处理新简历。可能已到达底部。');
        await autoScroll(iframeDoc); // 再尝试滚动一次
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const finalCheck = iframeDoc.querySelectorAll('#is-gray-batch-chat .card-list > li');
        if (finalCheck.length === newCardsAfterScroll.length) {
          console.log('二次滚动后卡片数量仍未增加，确认已到达底部，结束分析。');
          isAnalyzing = false;
          break;
        }
      }
    } else if (!allCurrentCardsSkippedOrProcessed) {
      // 如果在当前视口中找到了需要处理的卡片（即使它们后来处理失败）
      // 这不计为"空滚动"，因为我们至少尝试了。
      // 如果确实有简历被成功处理 (newResumeActuallyProcessedInThisPass is true), 计数器已在上面重置。
      // 如果尝试处理但都失败了，我们也不应该立即停止，除非重复多次。
      console.log('当前视口找到可处理的简历，或已处理简历。');
      if(!newResumeActuallyProcessedInThisPass && resumeCards.length > 0){
        console.log("虽然找到了可尝试处理的简历，但本轮实际没有成功处理任何新简历。");
        // 这里可以考虑是否也增加consecutiveScrollsWithoutNewProcessing，如果连续多次尝试但都失败。
        // 但目前逻辑是只要有尝试就不算"空"滚动。
      }
    } else if (resumeCards.length === 0 && isAnalyzing) {
        console.log('当前视口无简历卡片，但分析仍在进行中。可能是临时状态，等待下一轮。');
        await new Promise(resolve => setTimeout(resolve, 3000)); // 等待一下看看是否加载
    }


  } // 结束 while (isAnalyzing)

  if (button) { // 确保 button 对象存在
    if (!isAnalyzing && consecutiveScrollsWithoutNewProcessing < MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING) { // 正常完成或手动停止
      button.classList.remove('processing');
      button.classList.remove('success'); // 移除success类，保持按钮可点击
      button.style.backgroundColor = ''; // 重置背景色
      button.innerHTML = `
        <svg class="button-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M13 24q-2.5 0-4.75-0.95t-3.875-2.575q-1.625-1.625-2.575-3.875t-0.95-4.75q0-2.5 0.95-4.75t2.575-3.875q1.625-1.625 3.875-2.575t4.75-0.95q2.5 0 4.75 0.95t3.875 2.575q1.625 1.625 2.575 3.875t0.95 4.75q0 2.5-0.95 4.75t-2.575 3.875q-1.625 1.625-3.875 2.575t-4.75 0.95zM13 22q2.075 0 3.9-0.788t3.175-2.137q1.35-1.35 2.137-3.175t0.788-3.9q0-2.075-0.788-3.9t-2.137-3.175q-1.35-1.35-3.175-2.137t-3.9-0.788q-2.075 0-3.9 0.788t-3.175 2.137q-1.35-1.35-2.138 3.175t-0.787 3.9q0 2.075 0.787 3.9t2.138 3.175q1.35 1.35 3.175 2.137t3.9 0.788zM13 18q-1.65 0-2.825-1.175t-1.175-2.825q0-1.65 1.175-2.825t2.825-1.175q1.65 0 2.825 1.175t1.175 2.825q0 1.65-1.175 2.825t-2.825 1.175zM13 16q0.825 0 1.413-0.588t0.587-1.412q0-0.825-0.587-1.413t-1.413-0.587q-0.825 0-1.412 0.587t-0.588 1.413q0 0.825 0.588 1.412t1.412 0.588z"/>
        </svg>
        继续分析
      `;
      console.log('分析完成，按钮已重置为可继续分析状态');
    } else if (!isAnalyzing && consecutiveScrollsWithoutNewProcessing >= MAX_CONSECUTIVE_SCROLLS_WITHOUT_PROCESSING) { // 因卡住而停止
      button.classList.remove('processing');
      button.classList.remove('success'); // 移除success类
      button.classList.add('error'); // Or some other indicator for "stopped due to issue"
      button.style.backgroundColor = ''; // 重置背景色
      button.innerHTML = `
        <svg class="button-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M13 24q-2.5 0-4.75-0.95t-3.875-2.575q-1.625-1.625-2.575-3.875t-0.95-4.75q0-2.5 0.95-4.75t2.575-3.875q1.625-1.625 3.875-2.575t4.75-0.95q2.5 0 4.75 0.95t3.875 2.575q1.625 1.625 2.575 3.875t0.95 4.75q0 2.5-0.95 4.75t-2.575 3.875q-1.625 1.625-3.875 2.575t-4.75 0.95zM13 22q2.075 0 3.9-0.788t3.175-2.137q1.35-1.35 2.137-3.175t0.788-3.9q0-2.075-0.788-3.9t-2.137-3.175q-1.35-1.35-3.175-2.137t-3.9-0.788q-2.075 0-3.9 0.788t-3.175 2.137q-1.35-1.35-2.138 3.175t-0.787 3.9q0 2.075 0.787 3.9t2.138 3.175q1.35 1.35 3.175 2.137t3.9 0.788zM13 18q-1.65 0-2.825-1.175t-1.175-2.825q0-1.65 1.175-2.825t2.825-1.175q1.65 0 2.825 1.175t1.175 2.825q0 1.65-1.175 2.825t-2.825 1.175zM13 16q0.825 0 1.413-0.588t0.587-1.412q0-0.825-0.587-1.413t-1.413-0.587q-0.825 0-1.412 0.587t-0.588 1.413q0 0.825 0.588 1.412t1.412 0.588z"/>
        </svg>
        重新开始
      `;
      console.log('分析卡住，按钮已重置为可重新开始状态');
    }
  }
  
  console.log(`搜索页面简历分析结束。分析状态: ${isAnalyzing}, processedIds 数量: ${processedIds.size}, 连续空滚动次数: ${consecutiveScrollsWithoutNewProcessing}`);
}


// 处理聊天页面的简历分析
async function handleChatPageAnalysis(button) {
  console.log('开始处理聊天页面的简历分析');
  
  // 等待用户列表容器加载
  const userContainer = await waitForElement('.chat-user .user-container');
  console.log('用户列表容器已加载');
  
  // 记录已处理的简历ID，避免重复处理
  const processedIds = new Set();
  let previousItemCount = 0;
  let sameItemCountTimes = 0;
  
  // 初始清理所有可能存在的弹窗
  await closeAllResumeDialogs(document);
  
  // 设置防止长时间卡住的定时器
  let dialogTimeoutCheck;
  
  while (isAnalyzing) {
    // 清除上一个超时检查
    if (dialogTimeoutCheck) clearTimeout(dialogTimeoutCheck);
    
    // 设置新的超时检查
    dialogTimeoutCheck = setTimeout(async () => {
      if (isAnalyzing) {
        console.log('执行定期检查，查看是否有未关闭的弹窗...');
        await closeAllResumeDialogs(document);
      }
    }, 60000); // 每60秒检查一次
    
    // 获取所有用户列表项
    const userItems = document.querySelectorAll("#container > div:nth-child(1) > div > div.chat-box > div.chat-container > div.chat-user > div.user-container > div > div:nth-child(2) > div");
    console.log('找到用户列表项数量:', userItems.length);
    
    // 检测是否连续多次获取到相同数量的项目但未处理新简历
    if (userItems.length === previousItemCount) {
      sameItemCountTimes++;
      if (sameItemCountTimes >= 3) {
        console.log('连续多次获取到相同数量的用户且无新简历，可能已到达底部或加载异常，终止分析');
        break;
      }
    } else {
      previousItemCount = userItems.length;
      sameItemCountTimes = 0;
    }
    
    let newResumeFound = false;
    let allCurrentItemsProcessed = true;
    let processedInCurrentBatch = 0;
    
    // 逐个处理用户列表项
    for (const item of userItems) {
      if (!isAnalyzing) {
        console.log('分析过程被中断');
        return;
      }

      // 获取简历ID
      const resumeId = item.getAttribute('key')?.split('-')[0];
      
      // 检查这份简历是否已经分析过
      if (!resumeId) {
        console.log('无法获取简历ID，跳过');
        continue;
      }
      
      // 检查是否在当前会话中已处理过
      if (processedIds.has(resumeId)) {
        console.log(`跳过当前会话已处理的简历 ${resumeId}`);
        continue;
      }
      
      // 先将ID添加到已处理集合，防止异步处理过程中重复处理
      processedIds.add(resumeId);
      
      // 检查是否在历史记录中已分析过
      const isAnalyzed = await checkResumeAnalyzed(resumeId);
      if (isAnalyzed) {
        console.log(`跳过历史记录中已分析的简历 ${resumeId}`);
        continue;
      }

      try {
        console.log(`正在处理简历 ${resumeId}...`);
        newResumeFound = true;
        allCurrentItemsProcessed = false;
        
        // 在打开新简历前，先尝试关闭所有可能存在的简历弹窗
        await closeAllResumeDialogs(document);
        
        // 直接通过ID点击元素
        document.querySelector(`#_${resumeId}-0`).click();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 等待对话框出现
        const conversationBox = await waitForElement('.chat-conversation', 5000);
        if (!conversationBox) {
          throw new Error('点击后未能加载对话框');
        }
        console.log('已点击用户列表项，对话框已加载');
        
        // 等待基本信息区域加载
        const baseInfoNameElement = await waitForElement('.base-info-single-top-detail .base-info-item.name-contet > span > span > span');
        console.log('基本信息区域已加载');
        
        if (!isAnalyzing) return;
        
        // 从聊天界面提取基本信息
        const basicInfo = {};
        try {
          // 提取姓名
          basicInfo.name = baseInfoNameElement?.textContent?.trim() || '';
          
          // 提取性别
          const genderElement = document.querySelector('.base-info-single-top-detail .base-info-item:nth-child(2) span');
          if (genderElement) {
            const genderText = genderElement.textContent.trim();
            if (genderText.includes('男')) {
              basicInfo.gender = '男';
            } else if (genderText.includes('女')) {
              basicInfo.gender = '女';
            } else {
              basicInfo.gender = '';
            }
          }
          
          // 提取年龄
          const ageElement = document.querySelector('.base-info-single-top-detail .base-info-item:nth-child(2) span');
          if (ageElement) {
            const ageMatch = ageElement.textContent.match(/(\d+)岁/);
            if (ageMatch) {
              basicInfo.age = parseInt(ageMatch[1]);
            } else {
              basicInfo.age = null;
            }
          }
          
          // 提取工作经验
          const experienceElement = document.querySelector('.base-info-single-top-detail .base-info-item:nth-child(3) span');
          if (experienceElement) {
            basicInfo.experience = experienceElement.textContent.trim();
          } else {
            basicInfo.experience = '';
          }
          
          // 提取教育信息
          const educationElement = document.querySelector('.base-info-single-top-detail .base-info-item:nth-child(4) span');
          if (educationElement) {
            basicInfo.education = educationElement.textContent.trim();
            
            // 尝试提取学校和专业
            const eduInfo = {};
            const eduText = educationElement.textContent.trim();
            const eduParts = eduText.split('·');
            if (eduParts.length >= 1) {
              eduInfo.school = eduParts[0].trim();
            }
            if (eduParts.length >= 2) {
              eduInfo.major = eduParts[1].trim();
            }
            eduInfo.degree = basicInfo.education.includes('本科') ? '本科' : 
                            basicInfo.education.includes('硕士') ? '硕士' : 
                            basicInfo.education.includes('博士') ? '博士' : '';
            eduInfo.period = '';
            
            basicInfo.educationInfo = eduInfo;
          } else {
            basicInfo.education = '';
            basicInfo.educationInfo = { school: '', major: '', degree: '', period: '' };
          }
          
          // 提取求职状态
          const jobStatusElement = document.querySelector('.base-info-single-top-detail .base-info-item:nth-child(5) span');
          if (jobStatusElement) {
            basicInfo.jobStatus = jobStatusElement.textContent.trim();
          } else {
            basicInfo.jobStatus = '';
          }
          
          // 提取期望职位信息
          const expectElement = document.querySelector('.base-info-single-top-detail .base-info-item:nth-child(6) span');
          if (expectElement) {
            const expectText = expectElement.textContent.trim();
            const expectParts = expectText.split('·');
            
            basicInfo.expectInfo = {
              location: expectParts.length >= 1 ? expectParts[0].trim() : '',
              position: expectParts.length >= 2 ? expectParts[1].trim() : '',
              salary: expectParts.length >= 3 ? expectParts[2].trim() : ''
            };
          } else {
            basicInfo.expectInfo = { location: '', position: '', salary: '' };
          }
          
          // 初始化其他字段
          basicInfo.workExperiences = [];
          basicInfo.projectExperiences = [];
          basicInfo.selfDescription = '';
          basicInfo.resumeId = resumeId;
          
          console.log('已从聊天界面提取基本信息:', basicInfo);
        } catch (e) {
          console.warn('从聊天界面提取基本信息时出错:', e);
        }
        
        // 点击姓名打开简历详情
        await window.resumeManager.clickElement(baseInfoNameElement);
        console.log('已点击姓名打开简历详情');
        
        // 等待简历弹窗加载
        try {
          await waitForDialog(document);
          console.log('简历对话框已加载');
        } catch (error) {
          if (error.message === '用户中断操作') {
            return;
          }
          throw error;
        }
        
        if (!isAnalyzing) return;
        
        // 获取简历详细内容
        const detailedInfo = await window.resumeManager.extractChatPageResumeContent(document, resumeId);
        console.log('已提取简历详细内容');
        
        // 合并基本信息和详细信息
        const resumeContent = {
          ...basicInfo,
          ...detailedInfo,
          // 保留最有价值的信息，避免被默认值覆盖
          name: (basicInfo.name && basicInfo.name !== '未知') ? basicInfo.name : 
                (detailedInfo.name && detailedInfo.name !== '未知') ? detailedInfo.name : '未知',
          gender: (detailedInfo.gender && detailedInfo.gender !== '') ? detailedInfo.gender : 
                  (basicInfo.gender && basicInfo.gender !== '') ? basicInfo.gender : '',
          age: (detailedInfo.age && detailedInfo.age !== null) ? detailedInfo.age : 
               (basicInfo.age && basicInfo.age !== null) ? basicInfo.age : null,
          experience: detailedInfo.experience || basicInfo.experience || '',
          education: detailedInfo.education || basicInfo.education || '',
          jobStatus: detailedInfo.jobStatus || basicInfo.jobStatus || '',
          expectInfo: {
            location: detailedInfo.expectInfo?.location || basicInfo.expectInfo?.location || '',
            position: detailedInfo.expectInfo?.position || basicInfo.expectInfo?.position || '',
            salary: detailedInfo.expectInfo?.salary || basicInfo.expectInfo?.salary || ''
          },
          workExperiences: (detailedInfo.workExperiences && detailedInfo.workExperiences.length > 0) ? 
                          detailedInfo.workExperiences : 
                          (basicInfo.workExperiences && basicInfo.workExperiences.length > 0) ? 
                          basicInfo.workExperiences : [],
          educationInfo: (detailedInfo.educationInfo && detailedInfo.educationInfo.school) ? 
                        detailedInfo.educationInfo : 
                        (basicInfo.educationInfo && basicInfo.educationInfo.school) ? 
                        basicInfo.educationInfo : { school: '', major: '', degree: '', period: '' },
          selfDescription: detailedInfo.selfDescription || basicInfo.selfDescription || '',
          resumeId: resumeId
        };
        
        console.log('合并基本信息和详细信息完成');
        console.log('合并后的简历内容:', resumeContent);
        
        if (!isAnalyzing) return;
        
        // 调用AI评估
        try {
          console.log('开始AI评估');
          const score = await window.resumeManager.evaluateResume(resumeContent, document);
          console.log('评估完成，分数:', score);
          if (!isAnalyzing) return;
          
          // 如果分数达标，根据设置决定是否自动收藏和打招呼
          const threshold = await window.resumeManager.getUserScoreThreshold();
          if (score >= threshold) {
            console.log('分数达标');
            if (!isAnalyzing) return;
            
            // 检查是否需要自动收藏
            const shouldAutoFavorite = await window.resumeManager.getAutoFavoriteSetting();
            if (shouldAutoFavorite) {
              console.log('执行自动收藏');
              try {
                const favoriteResult = await window.resumeManager.favoriteResume();
                console.log('收藏结果:', favoriteResult ? '成功' : '失败');
              } catch (error) {
                console.error('自动收藏过程出错，但继续执行后续流程:', error);
              }
            }
            
            // 检查是否需要自动打招呼
            const shouldAutoGreet = await window.resumeManager.getAutoGreetSetting();
            if (shouldAutoGreet) {
              console.log('执行自动打招呼');
              await window.resumeManager.sendGreeting();
            }
          }
          
          // 保存已分析的简历ID
          await window.resumeManager.saveAnalyzedResumeId(resumeId);
          processedIds.add(resumeId); // 添加到当前会话的已处理集合中
          processedInCurrentBatch++;
          console.log(`已保存分析记录，当前批次已处理 ${processedInCurrentBatch} 份简历`);
          
        } catch (error) {
          console.error('AI评估失败:', error);
          // 确保关闭简历弹窗，即使评估失败
          await window.resumeManager.closeResume();
          await closeAllResumeDialogs(document);
          continue;
        }
        
        // 等待5秒后关闭简历弹窗
        await new Promise(resolve => setTimeout(resolve, 5000));
        if (!isAnalyzing) return;
        
        await window.resumeManager.closeResume();
        console.log('已关闭简历弹窗');
        if (!isAnalyzing) return;
        
        // 等待处理下一份简历
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(`处理简历 ${resumeId} 时出错:`, error);
        allCurrentItemsProcessed = false;
        if (isAnalyzing) {
          await window.resumeManager.closeResume();
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        if (!isAnalyzing) return;
      }
    }
    
    // 清除超时检查
    if (dialogTimeoutCheck) clearTimeout(dialogTimeoutCheck);
    
    // 只有当当前所有简历都处理完成后，才进行下一次滚动
    if (allCurrentItemsProcessed) {
      console.log(`当前页面所有简历已处理完成（共${processedIds.size}份），准备滚动加载更多...`);
      await autoScroll(document.querySelector("div.user-container > div"));
      
      // 等待新内容加载
      await new Promise(resolve => setTimeout(resolve, 3000)); // 延长等待时间
      
      // 检查是否有新的简历加载
      const newItems = document.querySelectorAll("#container > div:nth-child(1) > div > div.chat-box > div.chat-container > div.chat-user > div.user-container > div > div:nth-child(2) > div");
      if (newItems.length === userItems.length && !newResumeFound) {
        console.log('没有新的简历加载，可能已到达底部');
        // 再尝试滚动一次，确认是否真的到底了
        await autoScroll(document.querySelector("div.user-container"));
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const finalCheck = document.querySelectorAll("#container > div:nth-child(1) > div > div.chat-box > div.chat-container > div.chat-user > div.user-container > div > div:nth-child(2) > div");
        if (finalCheck.length === newItems.length) {
          console.log('确认已到达底部，结束分析');
          break;
        }
      }
    } else {
      console.log('当前页面还有未处理的简历，继续处理...');
    }
  }

  if (isAnalyzing) {
    button.classList.remove('processing');
    button.classList.add('success');
    button.innerHTML = `
      <svg class="button-icon" viewBox="0 0 24 24" width="16" height="16">
        <path fill="currentColor" d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
      </svg>
      分析完成
    `;
    console.log('聊天页面分析完成，按钮已重置为可继续分析状态');
  }
  
  console.log(`聊天页面简历分析完成，本次共处理 ${processedIds.size} 份简历`);
}

async function extractChatPageResumeContent(doc, resumeId) {
  try {
    // 等待简历详情加载完成（最多等待10秒）
    const startTime = Date.now();
    let resumeDetail = null;
    
    while (!resumeDetail && Date.now() - startTime < 10000) {
      // 从主文档中查找简历详情
      try {
        resumeDetail = doc.querySelector("div.resume-box");
      } catch (e) {
        console.warn('查找简历详情元素时出错:', e);
      }
      
      if (!resumeDetail) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    if (!resumeDetail) {
      console.warn('无法获取简历数据，返回空对象');
      return {
        resumeId,
        name: '',
        gender: '',
        age: null,
        experience: '',
        education: '',
        jobStatus: '',
        expectInfo: { location: '', position: '', salary: '' },
        workExperiences: [],
        projectExperiences: [],
        educationInfo: { school: '', major: '', degree: '', period: '' },
        selfDescription: '',
        timestamp: new Date().toISOString()
      };
    }

    console.log('找到简历详情元素');

    // 提取基本信息
    let baseInfo = {};
    try {
      baseInfo = {
        name: (resumeDetail.querySelector('.item-base-info .geek-name')?.textContent?.trim() || ''),
        gender: (resumeDetail.querySelector('.item-base-info .iboss-icon_man') || 
                resumeDetail.querySelector('[class^="iboss-icon_man"]')) ? '男' : 
                (resumeDetail.querySelector('.item-base-info .iboss-icon_women') || 
                resumeDetail.querySelector('[class^="iboss-icon_women"]')) ? '女' : '',
        isElite: !!resumeDetail.querySelector('img.elite'),
        activeStatus: resumeDetail.querySelector('.item-base-info .text-orange')?.textContent?.trim() || 
                      resumeDetail.querySelector('.item-base-info .active-status')?.textContent?.trim() || ''
      };
      console.log('提取基本信息成功:', baseInfo);
    } catch (e) {
      console.warn('提取基本信息时出错:', e);
      baseInfo = {
        name: '',
        gender: '',
        isElite: false,
        activeStatus: ''
      };
    }

    // 提取标签信息
    let labels = [];
    try {
      labels = Array.from(resumeDetail.querySelectorAll('.info-labels .label-text') || [])
        .map(span => span?.textContent?.trim() || '')
        .filter(Boolean);
      console.log('提取到标签信息:', labels);
    } catch (e) {
      console.warn('提取标签信息时出错:', e);
    }
    
    // 解析标签信息
    let age = '', experience = '', education = '', jobStatus = '';
    try {
      if (labels.length >= 1) age = labels[0];
      if (labels.length >= 2) experience = labels[1];
      if (labels.length >= 3) education = labels[2];
      if (labels.length >= 4) jobStatus = labels[3];
    } catch (error) {
      console.warn('解析标签信息失败:', error);
    }

    // 提取自我描述
    let selfDescription = '';
    try {
      selfDescription = resumeDetail.querySelector('.text.selfDescription')?.textContent?.trim() || '';
      console.log('提取自我描述成功:', selfDescription.substring(0, 50) + '...');
    } catch (e) {
      console.warn('提取自我描述时出错:', e);
    }

    // 提取期望职位信息
    let expectInfo = { location: '', position: '', salary: '' };
    try {
      const expectWrap = resumeDetail.querySelector('.geek-expect-wrap .join-text-wrap');
      if (expectWrap) {
        const spans = expectWrap.querySelectorAll('span.join-text') || [];
        if (spans.length >= 1) expectInfo.location = spans[0]?.textContent?.trim() || '';
        if (spans.length >= 2) expectInfo.position = spans[1]?.textContent?.trim() || '';
        if (spans.length >= 4) expectInfo.salary = spans[3]?.textContent?.trim() || '';
      }
      console.log('提取期望职位信息成功:', expectInfo);
    } catch (error) {
      console.warn('提取期望职位信息失败:', error);
    }

    // 提取工作经历
    let workExperiences = [];
    try {
      // 找到工作经历部分
      const workSection = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(item => 
        item.querySelector('h3.title')?.textContent?.trim() === '工作经历'
      );
      
      if (workSection) {
        const historyItems = workSection.querySelectorAll('.history-list .history-item') || [];
        workExperiences = Array.from(historyItems).map(item => {
          try {
            // 提取公司名称
            const companyElement = item.querySelector('.history-item-title .name span:first-child');
            const company = companyElement?.textContent?.trim() || '';
            
            // 提取职位
            const positionElement = item.querySelector('.history-item-title .name span:nth-child(3)');
            const position = positionElement?.textContent?.trim() || '';
            
            // 提取部门
            const departmentElement = item.querySelector('.history-item-title .name span:last-child');
            const department = departmentElement?.textContent?.trim() || '';
            
            // 提取工作时间
            const period = item.querySelector('.period')?.textContent?.trim() || '';
            
            // 提取工作内容 - 修复后的代码
            let content = '';
            const contentElement = item.querySelector('.item-text div[data-high-light="true"].text');
            if (contentElement) {
              content = contentElement.textContent.trim();
            }
            
            // 提取技能标签
            const skills = Array.from(item.querySelectorAll('.item-text .tags span') || [])
              .map(tag => tag?.textContent?.trim() || '')
              .filter(Boolean);
            
            return {
              company,
              position,
              department,
              period,
              content,
              skills
            };
          } catch (e) {
            console.warn('提取单个工作经历时出错:', e);
            return {
              company: '',
              position: '',
              department: '',
              period: '',
              content: '',
              skills: []
            };
          }
        });
      }
      console.log(`提取工作经历成功，共${workExperiences.length}条`);
    } catch (error) {
      console.warn('提取工作经历失败:', error);
    }

    // 提取项目经验
    let projectExperiences = [];
    try {
      // 找到项目经验部分
      const projectSection = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(item => 
        item.querySelector('h3.title')?.textContent?.trim() === '项目经验'
      );
      
      if (projectSection) {
        const historyItems = projectSection.querySelectorAll('.history-list .history-item') || [];
        projectExperiences = Array.from(historyItems).map(item => {
          try {
            // 提取项目名称和角色
            const nameElement = item.querySelector('.history-item-title .name');
            const nameText = nameElement?.textContent?.trim() || '';
            const nameParts = nameText.split(/\s*<em.*?vline.*?em>\s*|\s*·\s*/);
            
            const name = nameParts[0]?.trim() || '';
            const role = nameParts[1]?.trim() || '';
            
            // 提取项目时间
            const period = item.querySelector('.period')?.textContent?.trim() || '';
            
            // 提取项目内容
            const contentElement = item.querySelector('.text.project-content') || 
                                  item.querySelector('.item-text .text');
            const content = contentElement?.textContent?.trim() || '';
            
            return {
              name,
              role,
              period,
              content
            };
          } catch (e) {
            console.warn('提取单个项目经验时出错:', e);
            return {
              name: '',
              role: '',
              period: '',
              content: ''
            };
          }
        });
      }
      console.log(`提取项目经验成功，共${projectExperiences.length}条`);
    } catch (error) {
      console.warn('提取项目经验失败:', error);
    }

    // 提取教育经历
    let educationInfo = { school: '', major: '', degree: '', period: '' };
    try {
      // 找到教育经历部分
      const educationSection = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(item => 
        item.querySelector('h3.title')?.textContent?.trim() === '教育经历'
      );
      
      if (educationSection) {
        const educationItem = educationSection.querySelector('.history-item');
        if (educationItem) {
          const schoolElement = educationItem.querySelector('.name b');
          const majorElement = educationItem.querySelector('.major');
          const degreeElement = educationItem.querySelector('.name');
          const periodElement = educationItem.querySelector('.period');
          
          educationInfo = {
            school: schoolElement?.textContent?.trim() || '',
            major: majorElement?.textContent?.trim() || '',
            degree: degreeElement?.textContent?.includes('本科') ? '本科' : 
                   degreeElement?.textContent?.includes('硕士') ? '硕士' : 
                   degreeElement?.textContent?.includes('博士') ? '博士' : '',
            period: periodElement?.textContent?.trim() || ''
          };
        }
      }
      console.log('提取教育经历成功:', educationInfo);
    } catch (error) {
      console.warn('提取教育经历失败:', error);
    }

    // 尝试解析年龄为数字
    let ageNum = null;
    try {
      if (age) {
        const ageMatch = age.match(/(\d+)/);
        if (ageMatch) {
          ageNum = parseInt(ageMatch[1]);
        }
      }
    } catch (e) {
      console.warn('解析年龄为数字时出错:', e);
    }

    // 提取岗位经验
    let positionExperiences = [];
    try {
      const stationSection = resumeDetail.querySelector('.resume-item.resume-station');
      if (stationSection) {
        positionExperiences = Array.from(stationSection.querySelectorAll('.tags span'))
          .map(span => span?.textContent?.trim() || '')
          .filter(Boolean);
      }
      console.log('提取岗位经验成功:', positionExperiences);
    } catch (e) {
      console.warn('提取岗位经验时出错:', e);
    }

    // 提取关键词
    let keywords = [];
    try {
      const keywordsSection = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(item => 
        item.querySelector('h3.title')?.textContent?.trim() === '关键词'
      );
      
      if (keywordsSection) {
        keywords = Array.from(keywordsSection.querySelectorAll('.keywords'))
          .map(span => span?.textContent?.trim() || '')
          .filter(Boolean);
      }
      console.log(`提取关键词成功，共${keywords.length}个`);
    } catch (e) {
      console.warn('提取关键词时出错:', e);
    }

    // 提取资格证书
    let certificates = [];
    try {
      const certificateSection = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(item => 
        item.querySelector('h3.title')?.textContent?.trim() === '资格证书'
      );
      
      if (certificateSection) {
        certificates = Array.from(certificateSection.querySelectorAll('li'))
          .map(li => li?.textContent?.trim() || '')
          .filter(Boolean);
      }
      console.log(`提取资格证书成功，共${certificates.length}个`);
    } catch (e) {
      console.warn('提取资格证书时出错:', e);
    }

    const content = {
      resumeId,
      ...baseInfo,
      age: ageNum,
      experience,
      education,
      jobStatus,
      expectInfo,
      workExperiences,
      projectExperiences,
      educationInfo,
      selfDescription,
      positionExperiences,
      keywords,
      certificates,
      timestamp: new Date().toISOString()
    };

    console.log('成功提取简历内容');
    return content;
  } catch (error) {
    console.error('提取简历内容时发生错误:', error);
    // 即使发生错误，也返回一个包含基本信息的对象
    return {
      resumeId,
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: { location: '', position: '', salary: '' },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: { school: '', major: '', degree: '', period: '' },
      selfDescription: '',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}



// 处理推荐页面的简历分析
async function handleRecommendPageAnalysis(button) {
  console.log('开始处理推荐页面的简历分析');
  // 获取 iframe 内的文档
  const recommendFrame = document.querySelector('iframe[name="recommendFrame"]');
  if (!recommendFrame) {
    throw new Error('找不到推荐 iframe');
  }
  const iframeDoc = recommendFrame.contentDocument || recommendFrame.contentWindow.document;
  console.log('已获取 iframe 文档');

  // 等待简历列表加载完成
  await waitForAnyCardList(iframeDoc);
  console.log('简历列表已加载');
  
  // 初始清理所有可能存在的弹窗
  await closeAllResumeDialogs(document);
  await closeAllResumeDialogs(iframeDoc);
  
  // 记录已处理的简历ID，避免重复处理
  const processedIds = new Set();
  let previousCardCount = 0;
  let sameCardCountTimes = 0;
  
  // 设置防止长时间卡住的定时器
  let dialogTimeoutCheck;
  
  while (isAnalyzing) {
    
    
    // 获取当前可见的简历卡片，并标记来源
    const cardItemCards = Array.from(iframeDoc.querySelector("#recommend-list").querySelectorAll('.card-item'));
    const cardInnerCards = Array.from(iframeDoc.querySelector("#recommend-list").querySelectorAll('.card-inner'));

    // 标记卡片来源
    let cardSource = '';
    let resumeCards;

    if (cardItemCards.length > 0) {
      resumeCards = cardItemCards;
      cardSource = 'recommend-geek-card-item';
      console.log('找到recommend-geek-card-item类型的简历卡片数量:', resumeCards.length);
    } else {
      resumeCards = cardInnerCards;
      cardSource = 'new-geek-card-inner';
      console.log('找到new-geek-card-inner类型的简历卡片数量:', resumeCards.length);
    }

    // 后续可以根据cardSource值判断处理流程
    console.log('简历卡片来源:', cardSource);
    
    // 检测是否连续多次获取到相同数量的卡片但未处理新简历
    if (resumeCards.length === previousCardCount) {
      sameCardCountTimes++;
      if (sameCardCountTimes >= 3) {
        console.log('连续多次获取到相同数量的卡片且无新简历，可能已到达底部或加载异常，终止分析');
        break;
      }
    } else {
      previousCardCount = resumeCards.length;
      sameCardCountTimes = 0;
    }
    
    let newResumeFound = false;
    let allCurrentCardsProcessed = true;
    let processedInCurrentBatch = 0;
    let resumeId = null;
    let basicInfo = null;
    let cardInner = null;
    
    // 逐个处理简历卡片
    for (const card of resumeCards) {
      if (!isAnalyzing) {
        console.log('分析过程被中断');
        return;
      }
      console.log('开始处理简历卡片');
      console.log(card);

      if(cardSource === 'recommend-geek-card-item'){

        // 获取简历ID
        cardInner = card.querySelector('.card-inner');
        if (!cardInner) {
          console.log('无法获取卡片内容，跳过');
          continue;
        }
        // 从卡片中提取基本信息
        basicInfo = window.resumeManager.extractCardInnerInfo(cardInner);
        resumeId = basicInfo.resumeId;
      }
    

      if(cardSource === 'new-geek-card-inner') {
        // 获取简历基本信息
        basicInfo = window.resumeManager.extractNewGeekCardInfo(card);
        resumeId = basicInfo.resumeId;
        cardInner = card;
      }

      if (!resumeId) {
        console.log('无法获取新版卡片简历ID，跳过');
        continue;
      }

      // 检查这份简历是否已经分析过
      if (processedIds.has(resumeId)) {
        console.log(`跳过当前会话已处理的简历 ${resumeId}`);
        continue;
      }
        
      // 先将ID添加到已处理集合，防止异步处理过程中重复处理
      processedIds.add(resumeId);
      
      // 检查是否在历史记录中已分析过
      const isAnalyzed = await checkResumeAnalyzed(resumeId);
      if (isAnalyzed) {
        console.log(`跳过历史记录中已分析的简历 ${resumeId}`);
        continue;
      }

      try {
        console.log(`正在处理简历 ${resumeId}...`);
        newResumeFound = true;
        allCurrentCardsProcessed = false;
        
        // 在打开新简历前，先尝试关闭所有可能存在的简历弹窗
        await closeAllResumeDialogs(document);
        await closeAllResumeDialogs(iframeDoc);
        
        // 点击卡片打开简历
        await window.resumeManager.clickElement(cardInner);
        console.log('已点击简历卡片');
        
        // 等待对话框加载
        try {
          await waitForDialog(iframeDoc);
          console.log('简历对话框已加载');
        } catch (error) {
          if (error.message === '用户中断操作') {
            return;
          }
          throw error;
        }
        
        if (!isAnalyzing) return;
        
        // 获取简历详细内容
        if (cardSource === 'recommend-geek-card-item'){
          detailedInfo = await window.resumeManager.extractRecommendPageResumeContent(iframeDoc, resumeId);
        } else {
          detailedInfo = await window.resumeManager.extractNewRecommendPageResumeContent(iframeDoc, resumeId);
        }
        console.log('已提取简历详细内容');
        
        // 合并基本信息和详细信息
        const resumeContent = {
          ...basicInfo,
          ...detailedInfo,
          // 保留最有价值的信息，避免被默认值覆盖
          name: (basicInfo.name && basicInfo.name !== '未知') ? basicInfo.name : 
                (detailedInfo.name && detailedInfo.name !== '未知') ? detailedInfo.name : '未知',
          gender: (basicInfo.gender && basicInfo.gender !== '未知') ? basicInfo.gender : 
                  (detailedInfo.gender && detailedInfo.gender !== '未知') ? detailedInfo.gender : '未知',
          age: (basicInfo.age && basicInfo.age !== null) ? basicInfo.age : 
                (detailedInfo.age && detailedInfo.age !== null) ? detailedInfo.age : null,
          experience: basicInfo.experience || detailedInfo.experience || '',
          education: basicInfo.education || detailedInfo.education || '',
          jobStatus: basicInfo.jobStatus || detailedInfo.jobStatus || '',
          expectInfo: {
            location: detailedInfo.expectInfo?.location || basicInfo.expectInfo?.location || '',
            position: detailedInfo.expectInfo?.position || basicInfo.expectInfo?.position || '',
            salary: detailedInfo.expectInfo?.salary || basicInfo.expectInfo?.salary || ''
          },
          workExperiences: (detailedInfo.workExperiences && detailedInfo.workExperiences.length > 0) ? 
                          detailedInfo.workExperiences : 
                          (basicInfo.workExperiences && basicInfo.workExperiences.length > 0) ? 
                          basicInfo.workExperiences : [],
          educationInfo: (detailedInfo.educationInfo && detailedInfo.educationInfo.school) ? 
                        detailedInfo.educationInfo : 
                        (basicInfo.educationInfo && basicInfo.educationInfo.school) ? 
                        basicInfo.educationInfo : { school: '', major: '', degree: '', period: '' },
          selfDescription: detailedInfo.selfDescription || basicInfo.selfDescription || '',
          resumeId: resumeId
        };

        console.log('合并基本信息和详细信息完成');
        console.log(resumeContent);
        
        if (!isAnalyzing) return;
        
        // 调用AI评估
        try {
          console.log('开始AI评估');
          const score = await window.resumeManager.evaluateResume(resumeContent, recommendFrame);
          console.log('评估完成，分数:', score);
          if (!isAnalyzing) return;
          
          // 如果分数达标，根据设置决定是否自动收藏和打招呼
          const threshold = await window.resumeManager.getUserScoreThreshold();
          if (score >= threshold) {
            console.log('分数达标');
            if (!isAnalyzing) return;
            
            // 检查是否需要自动收藏
            const shouldAutoFavorite = await window.resumeManager.getAutoFavoriteSetting();
            if (shouldAutoFavorite) {
              console.log('执行自动收藏');
              try {
                const favoriteResult = await window.resumeManager.favoriteResume(iframeDoc);
                console.log('收藏结果:', favoriteResult ? '成功' : '失败');
              } catch (error) {
                console.error('自动收藏过程出错，但继续执行后续流程:', error);
              }
            }
            
            // 检查是否需要自动打招呼
            const shouldAutoGreet = await window.resumeManager.getAutoGreetSetting();
            if (shouldAutoGreet) {
              console.log('执行自动打招呼');
              await window.resumeManager.sendGreeting(iframeDoc);
            }

            
          }
          
          // 保存已分析的简历ID到 storage
          await new Promise((resolve) => {
            chrome.storage.local.get(['analyzedResumeIds'], (result) => {
              const analyzedIds = new Set(result.analyzedResumeIds || []);
              analyzedIds.add(resumeId);
              chrome.storage.local.set({
                'analyzedResumeIds': Array.from(analyzedIds)
              }, resolve);
            });
          });
          
          processedIds.add(resumeId); // 添加到当前会话的已处理集合中
          processedInCurrentBatch++;
          console.log(`已保存分析记录，当前批次已处理 ${processedInCurrentBatch} 份简历`);
          
        } catch (error) {
          console.error('AI评估失败:', error);
          // 确保关闭简历弹窗，即使评估失败
          await window.resumeManager.closeResume(iframeDoc);
          await closeAllResumeDialogs(document);
          await closeAllResumeDialogs(iframeDoc);
          continue;
        }
        
        // 等待5秒后关闭简历弹窗
        await new Promise(resolve => setTimeout(resolve, 5000));
        if (!isAnalyzing) return;
        
        await window.resumeManager.closeResume(iframeDoc);
        console.log('已关闭简历弹窗');
        if (!isAnalyzing) return;
        
        // 等待处理下一份简历
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`处理简历 ${resumeId} 时出错:`, error);
        allCurrentCardsProcessed = false;
        if (isAnalyzing) {
          await window.resumeManager.closeResume();
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        if (!isAnalyzing) return;
      }
    }
      
    // 清除超时检查
    if (dialogTimeoutCheck) clearTimeout(dialogTimeoutCheck);
    
    // 检查是否需要加载更多
    if (allCurrentCardsProcessed) {
      console.log(`当前页面所有简历已处理完成（共${processedIds.size}份），准备滚动加载更多...`);
      await autoScroll(document);
      
      // 等待新内容加载
      await new Promise(resolve => setTimeout(resolve, 3000)); // 延长等待时间
      
      // 检查是否有新的简历加载
      let newCards;
      if (cardSource === 'recommend-geek-card-item') {
        newCards = iframeDoc.querySelector("#recommend-list").querySelectorAll('.card-item');
      } else {
        newCards = iframeDoc.querySelector("#recommend-list").querySelectorAll('.card-inner');
      }
      
      // 计算新加载的卡片数量
      const newCardsCount = newCards.length - resumeCards.length;
      console.log(`检测到${newCardsCount}个新加载的简历卡片`);
      
      // 如果有新卡片，且数量达到或超过15个，直接处理而不继续滚动
      if (newCardsCount >= 15) {
        console.log(`新加载的卡片数量(${newCardsCount})已达到15个，开始处理...`);
        continue; // 返回循环开始，处理新卡片
      }
      
      if (newCards.length === resumeCards.length && !newResumeFound) {
        console.log('没有新的简历加载，可能已到达底部');
        // 再尝试滚动一次，确认是否真的到底了
        await autoScroll(iframeDoc);
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 再次使用相同的逻辑检查新卡片
        let finalCheck;
        if (cardSource === 'recommend-geek-card-item') {
          finalCheck = iframeDoc.querySelector("#recommend-list").querySelectorAll('.card-item');
        } else {
          finalCheck = iframeDoc.querySelector("#recommend-list").querySelectorAll('.card-inner');
        }
        
        if (finalCheck.length === newCards.length) {
          console.log('确认已到达底部，结束分析');
          break;
        }
      }
    } else {
      console.log('当前页面还有未处理的简历，继续处理...');
    }
  }

  if (isAnalyzing) {
    button.classList.remove('processing');
    button.classList.add('success');
    button.innerHTML = `
      <svg class="button-icon" viewBox="0 0 24 24" width="16" height="16">
        <path fill="currentColor" d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
      </svg>
      分析完成
    `;
    console.log('推荐页面分析完成，按钮已重置为可继续分析状态');
  }
  
  console.log(`推荐页面简历分析完成，本次共处理 ${processedIds.size} 份简历`);
}

// 从推荐列表中的 cardInner 元素中提取基本信息
function extractCardInnerInfo(cardInner) {
  try {
    console.log('从卡片中提取基本信息');
    
    if (!cardInner) {
      throw new Error('卡片元素为空');
    }
    
    // 获取简历ID
    const resumeId = cardInner.getAttribute('data-geek') || cardInner.getAttribute('data-geekid');
    
    // 获取姓名
    const nameElement = cardInner.querySelector('.col-2 .name-wrap .name');
    const name = nameElement ? nameElement.textContent.trim() : '未知姓名';
    
    // 获取活跃状态
    const activeElement = cardInner.querySelector('.col-2 .name-wrap .active-text');
    const activeStatus = activeElement ? activeElement.textContent.trim() : '';
    
    // 获取薪资
    const salaryElement = cardInner.querySelector('.col-1 .salary-wrap');
    const salary = salaryElement ? salaryElement.textContent.trim() : '';
    
    // 获取性别
    const genderElement = cardInner.querySelector('.col-1 .gender');
    const gender = genderElement ? 
                  (genderElement.classList.contains('iboss-icon_man') ? '男' : 
                   genderElement.classList.contains('iboss-icon_women') ? '女' : '未知') : '未知';
    
    // 获取基本信息（年龄、工作经验、学历、求职状态）
    const baseInfoElement = cardInner.querySelector('.col-2 .base-info');
    let age = null, experience = '', education = '', jobStatus = '';
    
    if (baseInfoElement) {
      const baseInfoText = baseInfoElement.textContent.trim();
      const baseInfoParts = baseInfoText.split(/\s*[^\d\w\u4e00-\u9fa5]+\s*/); // 按非数字、字母、汉字的字符分割
      
      // 提取年龄
      const ageMatch = baseInfoParts[0] ? baseInfoParts[0].match(/(\d+)岁/) : null;
      age = ageMatch ? parseInt(ageMatch[1]) : null;
      
      // 提取工作经验
      experience = baseInfoParts[1] || '';
      
      // 提取学历
      education = baseInfoParts[2] || '';
      
      // 提取求职状态
      jobStatus = baseInfoParts[3] || '';
    }
    
    // 获取期望信息
    const expectElement = cardInner.querySelector('.col-2 .row-flex .content');
    let expectLocation = '', expectPosition = '';
    
    if (expectElement) {
      const expectText = expectElement.textContent.trim();
      const expectParts = expectText.split(/\s*[^\d\w\u4e00-\u9fa5]+\s*/);
      expectLocation = expectParts[0] || '';
      expectPosition = expectParts[1] || '';
    }
    
    // 获取个人优势
    const advantageElement = cardInner.querySelector('.col-2 .geek-desc .content');
    const advantage = advantageElement ? advantageElement.textContent.trim() : '';
    
    // 获取标签
    const tagElements = cardInner.querySelectorAll('.col-2 .tags .tag-item');
    const tags = Array.from(tagElements).map(tag => tag.textContent.trim());
    
    // 获取工作经历
    const workItems = cardInner.querySelectorAll('.col-3 .work-exps .timeline-item');
    const workExperiences = Array.from(workItems).map(item => {
      const timeElement = item.querySelector('.time');
      const contentElement = item.querySelector('.content');
      
      if (!timeElement || !contentElement) return null;
      
      const timeText = timeElement.textContent.trim();
      const contentText = contentElement.textContent.trim();
      
      // 提取时间段
      const timeParts = timeText.split(/\s*[^\d\w\u4e00-\u9fa5\.]+\s*/);
      const startTime = timeParts[0] || '';
      const endTime = timeParts[1] || '';
      
      // 提取公司和职位
      const contentParts = contentText.split(/\s*[^\d\w\u4e00-\u9fa5]+\s*/);
      const company = contentParts[0] || '';
      const position = contentParts[1] || '';
      
      return {
        company,
        position,
        period: `${startTime}-${endTime}`,
        startTime,
        endTime
      };
    }).filter(item => item !== null);
    
    // 获取教育经历
    const eduItems = cardInner.querySelectorAll('.col-3 .edu-exps .timeline-item');
    const educationExperiences = Array.from(eduItems).map(item => {
      const timeElement = item.querySelector('.time');
      const contentElement = item.querySelector('.content');
      
      if (!timeElement || !contentElement) return null;
      
      const timeText = timeElement.textContent.trim();
      const contentText = contentElement.textContent.trim();
      
      // 提取时间段
      const timeParts = timeText.split(/\s*[^\d\w\u4e00-\u9fa5\.]+\s*/);
      const startTime = timeParts[0] || '';
      const endTime = timeParts[1] || '';
      
      // 提取学校、专业和学历
      const contentParts = contentText.split(/\s*[^\d\w\u4e00-\u9fa5]+\s*/);
      const school = contentParts[0] || '';
      const major = contentParts[1] || '';
      const degree = contentParts[2] || '';
      
      return {
        school,
        major,
        degree,
        period: `${startTime}-${endTime}`,
        startTime,
        endTime
      };
    }).filter(item => item !== null);
    
    // 构建结果对象
    const result = {
      resumeId,
      name,
      gender,
      activeStatus,
      salary,
      age,
      experience,
      education,
      jobStatus,
      expectInfo: {
        location: expectLocation,
        position: expectPosition
      },
      advantage,
      tags,
      workExperiences,
      educationInfo: educationExperiences.length > 0 ? educationExperiences[0] : {},
      educationExperiences,
      timestamp: new Date().toISOString()
    };
    
    console.log('提取的卡片基本信息：', result);
    return result;
    
  } catch (error) {
    console.error('提取卡片基本信息时出错:', error);
    return {
      resumeId: cardInner ? (cardInner.getAttribute('data-geek') || cardInner.getAttribute('data-geekid')) : null,
      name: '提取失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

async function extractRecommendPageResumeContent(doc, resumeId) {
  try {
    // 等待简历详情加载完成（最多等待10秒）
    const startTime = Date.now();
    let resumeDetail = null;
    
    while (!resumeDetail && Date.now() - startTime < 10000) {
      try {
        resumeDetail = doc.querySelector(".resume-detail-wrap");
      } catch (e) {
        console.warn('查找简历详情元素时出错:', e);
      }
      
      if (!resumeDetail) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    let result = {
      resumeId,
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: { location: '', position: '', salary: '', industry: '' },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: { school: '', major: '', degree: '', period: '', experience: '' },
      selfDescription: '',
      timestamp: new Date().toISOString()
    };

    if (!resumeDetail) {
      console.warn('无法获取简历数据，返回空对象');
      return result;
    }

    if (doc.querySelector('iframe[src*="/web/frame/c-resume/"]')) {
      console.log('检测到简历iframe，尝试Canvas提取');
      try {
        const canvas_result = await extractResumeCanvas(document);
        console.log('Canvas提取结果:', canvas_result ? '成功' : '失败');
        
        if (canvas_result && canvas_result.trim()) {
          const ocr_result = await extractResumeText(canvas_result);
          result.name = ocr_result.name;
          result.gender = ocr_result.gender;
          result.age = ocr_result.age;
          result.experience = ocr_result.experience;
          result.education = ocr_result.education;
          result.jobStatus = ocr_result.jobStatus;
          result.expectInfo.location = ocr_result.expectInfo.location;
          result.expectInfo.position = ocr_result.expectInfo.position;
          result.expectInfo.salary = ocr_result.expectInfo.salary;
          result.expectInfo.industry = ocr_result.expectInfo.industry;
          result.selfDescription = ocr_result.selfDescription;
          result.workExperiences = ocr_result.workExperiences;
          result.projectExperiences = ocr_result.projectExperiences;
          result.educationInfo = ocr_result.educationInfo;
          result.timestamp = new Date().toISOString();
          console.log('Canvas提取成功，返回解析结果');
          return result;
        } else {
          console.warn('Canvas提取失败或为空，尝试DOM提取');
        }
      } catch (error) {
        console.error('Canvas提取过程中出错:', error);
        console.log('尝试DOM提取作为备选方案');
      }
    }

    // 如果Canvas提取失败，使用DOM提取作为备选方案
    console.log('使用DOM提取作为备选方案');

    // 提取基本信息
    let baseInfo = {};
    
    try {
      baseInfo = {
        name: (''),
        gender: '',
        isElite: !!resumeDetail.querySelector('img.elite'),
        activeStatus: resumeDetail.querySelector('.text-orange, .active-status')?.textContent?.trim() || ''
      };
      const genderMale = resumeDetail.querySelector('[class^="iboss-icon_man"]'); // 以iboss-icon_man开头的类
      const genderFemale = resumeDetail.querySelector('[class^="iboss-icon_women"]'); // 以iboss-icon_women开头的类
      const gender = genderMale ? '男' : (genderFemale ? '女' : '');
      baseInfo.gender = gender;
      
      // 提取自我描述（从基本信息部分提取）
      const descElement = resumeDetail.querySelector('.geek-base-info-wrap .geek-desc');
      if (descElement) {
        baseInfo.selfDescription = descElement.textContent.trim();
      }
      
      console.log('提取基本信息成功:', baseInfo);
    } catch (e) {
      console.warn('提取基本信息时出错:', e);
      baseInfo = {
        name: '',
        gender: '',
        isElite: false,
        activeStatus: '',
        selfDescription: ''
      };
    }

    // 提取岗位经验
    let experience = '';
    try {
      const positionExpElement = resumeDetail.querySelector('.geek-position-experience-wrap .tags .tag');
      if (positionExpElement) {
        const expText = positionExpElement.textContent.trim();
        const expMatch = expText.match(/(\d+)年(\d+)个月/);
        if (expMatch) {
          experience = `${expMatch[1]}年${expMatch[2]}个月`;
        }
      }
    } catch (e) {
      console.warn('提取岗位经验时出错:', e);
    }

    // 提取期望职位信息
    let expectInfo = { location: '', position: '', salary: '', industry: '' };
    try {
      const expectWrap = resumeDetail.querySelector('.geek-expect-wrap .join-text-wrap');
      if (expectWrap) {
        const spans = Array.from(expectWrap.querySelectorAll('span')).map(span => span.textContent.trim());
        if (spans.length >= 1) expectInfo.location = spans[0];
        if (spans.length >= 2) expectInfo.position = spans[1];
        if (spans.length >= 3) expectInfo.industry = spans[2];
        if (spans.length >= 4) expectInfo.salary = spans[3];
      }
      console.log('提取期望职位信息成功:', expectInfo);
    } catch (error) {
      console.warn('提取期望职位信息失败:', error);
    }

    // 提取工作经历
    let workExperiences = [];
    try {
      const workSection = resumeDetail.querySelector('.geek-work-experience-wrap');
      if (workSection) {
        const workWraps = workSection.querySelectorAll('.work-wrap');
        workExperiences = Array.from(workWraps).map(work => {
          try {
            const companyName = work.querySelector('.company-name-wrap .company-name, .company-name-wrap .name')?.textContent?.trim() || '';
            const position = work.querySelector('.company-name-wrap .position span:first-child')?.textContent?.trim() || '';
            const department = work.querySelector('.company-name-wrap .position span:last-child')?.textContent?.trim().replace(' · ', '') || '';
            const period = work.querySelector('.period')?.textContent?.trim() || '';
            const content = work.querySelector('.item-content')?.textContent?.trim() || '';
            const isInternship = !!work.querySelector('.practice-tag');
            
            // 提取技能标签
            const skills = Array.from(work.querySelectorAll('.tags .tag') || [])
              .map(tag => tag?.textContent?.trim())
              .filter(Boolean);
            
            return {
              company: companyName,
              position,
              department,
              period,
              content,
              isInternship,
              skills
            };
          } catch (e) {
            console.warn('提取单个工作经历时出错:', e);
            return null;
          }
        }).filter(Boolean);
      }
      console.log(`提取工作经历成功，共${workExperiences.length}条`);
    } catch (error) {
      console.warn('提取工作经历失败:', error);
    }

    // 提取项目经验
    let projectExperiences = [];
    try {
      const projectSection = resumeDetail.querySelector('.geek-project-experience-wrap');
      if (projectSection) {
        const projectWraps = projectSection.querySelectorAll('.project-wrap');
        projectExperiences = Array.from(projectWraps).map(project => {
          try {
            const nameWrap = project.querySelector('.name.join-text-wrap');
            const spans = nameWrap ? Array.from(nameWrap.querySelectorAll('span')).map(span => span.textContent.trim()) : [];
            
            const name = spans[0] || '';
            const role = spans[1] || '';
            const period = project.querySelector('.period')?.textContent?.trim() || '';
            
            // 提取业绩和内容
            const performance = project.querySelector('.item-wrap:nth-child(2) .item-content')?.textContent?.trim() || '';
            const content = project.querySelector('.item-wrap:nth-child(3) .item-content')?.textContent?.trim() || '';
            
            return {
              name,
              role,
              period,
              performance,
              content
            };
          } catch (e) {
            console.warn('提取单个项目经验时出错:', e);
            return null;
          }
        }).filter(Boolean);
      }
      console.log(`提取项目经验成功，共${projectExperiences.length}条`);
    } catch (error) {
      console.warn('提取项目经验失败:', error);
    }

    // 提取教育经历
    let educationInfo = { school: '', major: '', degree: '', period: '', experience: '' };
    try {
      const educationSection = resumeDetail.querySelector('.geek-education-experience-wrap');
      if (educationSection) {
        const eduWrap = educationSection.querySelector('.edu-wrap');
        if (eduWrap) {
          const schoolName = eduWrap.querySelector('.school-name')?.textContent?.trim() || '';
          const major = eduWrap.querySelector('.major')?.textContent?.trim() || '';
          const degree = eduWrap.querySelector('.school-name-wrap span:nth-child(5)')?.textContent?.trim() || '';
          const period = eduWrap.querySelector('.period')?.textContent?.trim() || '';
          const experience = eduWrap.querySelector('.text-item .title + div')?.textContent?.trim() || '';
          
          educationInfo = {
            school: schoolName,
            major,
            degree,
            period,
            experience
          };
        }
      }
      console.log('提取教育经历成功:', educationInfo);
    } catch (error) {
      console.warn('提取教育经历失败:', error);
    }

    const content = {
      resumeId,
      ...baseInfo,
      experience,
      education: educationInfo.degree || '',
      jobStatus: baseInfo.activeStatus || '',
      expectInfo,
      workExperiences,
      projectExperiences,
      educationInfo,
      selfDescription: baseInfo.selfDescription || '',
      timestamp: new Date().toISOString()
    };
    console.log('成功提取简历内容:', content);
    return content;
  } catch (error) {
    console.error('提取简历内容时发生错误:', error);
    // 即使发生错误，也返回一个包含基本信息的对象
    return {
      resumeId,
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: { location: '', position: '', salary: '', industry: '' },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: { school: '', major: '', degree: '', period: '', experience: '' },
      selfDescription: '',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

async function extractResumeText(canvas_result) {
  // 处理文本，去掉重复的换行符
  const text = canvas_result ? canvas_result.replace(/\n+/g, '\n').trim() : '';
  
  // 如果文本为空，直接返回空结果
  if (!text) {
    console.warn('Canvas提取的文本为空，返回空结果');
    return {
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: {
        location: '',
        position: '',
        salary: '',
        industry: ''
      },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: {
        school: '',
        major: '',
        degree: '',
        period: '',
        experience: ''
      },
      selfDescription: ''
    };
  }
  
  console.log("准备解析文本，长度:", text.length, "预览:", text.substring(0, 200), "...");
  
  try {
    // 调用后端API处理简历文本
    const base_url = await getApiEndpoint();
    const token = await getApiKey();
    const response = await fetch(`${base_url}/api/text-to-resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ text })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const result = await response.json();
    console.log('简历解析结果:', result);
    return result;

  } catch (error) {
    console.error('调用简历解析API时出错:', error);
    // 返回空结果
    return {
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: {
        location: '',
        position: '',
        salary: '',
        industry: ''
      },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: {
        school: '',
        major: '',
        degree: '',
        period: '',
        experience: ''
      },
      selfDescription: ''
    };
  }
}

// 在处理简历之前，先从 storage 中读取已分析的简历 ID
async function checkResumeAnalyzed(resumeId) {
  return new Promise((resolve) => {
    chrome.storage.local.get(['analyzedResumeIds'], (result) => {
      const analyzedIds = new Set(result.analyzedResumeIds || []);
      resolve(analyzedIds.has(resumeId));
    });
  });
}

async function extractRecommendPageResumeContentCommon(doc) {
  console.log('extractRecommendPageResumeContentCommon');
  const resumeDetail = doc.querySelector(".resume-detail-wrap");
  if (!resumeDetail) {
    console.warn('未找到简历详情元素');
    return null;
  }
  return resumeDetail;
}

async function extractChatPageResumeContentCommon(doc) {
  console.log('extractChatPageResumeContentCommon');
  const resumeDetail = doc.querySelector("div.resume-box");
  if (!resumeDetail) {
    console.warn('未找到简历详情元素');
    return null;
  }
  return resumeDetail;
}

async function extractSearchPageResumeContentCommon(doc) {
  console.log('extractSearchPageResumeContentCommon');
  const resumeDetail = doc.querySelector("div.resume-layout-wrap > div > div.resume-center-side > div");
  if (!resumeDetail) {
    console.warn('未找到简历详情元素');
    return null;
  }
  return resumeDetail;
}

// 添加一个尝试关闭所有可能开着的简历弹窗的函数
async function closeAllResumeDialogs(doc = document) {
  console.log('尝试关闭所有可能存在的简历弹窗');
  try {
    // 查找所有可能的简历弹窗关闭按钮
    const closeButtons = doc.querySelectorAll(
      '.boss-dialog__wrapper.dialog-lib-resume .dialog-close, ' +
      '.boss-dialog__wrapper.resume-common-dialog.search-resume.new-chat-resume-dialog-main-ui.resume-container > div.boss-popup__close, ' +
      '.boss-dialog__wrapper .dialog-close, ' +
      'div.boss-popup__close'
    );
    
    if (closeButtons.length > 0) {
      console.log(`找到${closeButtons.length}个简历弹窗，尝试关闭`);
      // 依次点击所有关闭按钮
      for (const button of closeButtons) {
        button.click();
        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂等待确保DOM响应
      }
      
      // 等待确保弹窗真的关闭了
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 再次检查是否还有弹窗存在
      const remainingDialogs = doc.querySelectorAll(
        '.boss-dialog__wrapper.dialog-lib-resume, ' +
        '.boss-dialog__wrapper.resume-common-dialog.search-resume, ' +
        '.boss-dialog__wrapper'
      );
      
      if (remainingDialogs.length > 0) {
        console.log(`仍有${remainingDialogs.length}个弹窗未关闭，再次尝试`);
        // 再次尝试关闭
        for (const dialog of remainingDialogs) {
          const closeBtn = dialog.querySelector('.dialog-close, .boss-popup__close');
          if (closeBtn) closeBtn.click();
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } else {
      console.log('未发现打开的简历弹窗');
    }
  } catch (error) {
    console.error('关闭简历弹窗时发生错误:', error);
  }
}

// 向页面添加样式
function injectAnalysisStyles() {
  if (document.getElementById('ai-analysis-styles')) return;
  
  const style = document.createElement('style');
  style.id = 'ai-analysis-styles';
  style.textContent = `
    .ai-analysis-status {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      animation: fadeIn 0.3s ease;
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;
    }
    
    .ai-analysis-status div, .ai-analysis-status pre {
      margin-bottom: 6px;
      word-break: break-word;
      position: relative;
    }
    
    .ai-analysis-status button {
      opacity: 0.7;
      transition: opacity 0.2s;
    }
    
    .ai-analysis-status button:hover {
      opacity: 1;
    }
    
    .thinking-container {
      border-left: 3px solid #4a90e2;
      padding-left: 10px;
      background-color: rgba(74, 144, 226, 0.1);
      border-radius: 0 4px 4px 0;
      margin-bottom: 15px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .status-complete {
      animation: pulse 1s ease;
      font-weight: bold;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      padding-top: 8px;
      margin-top: 8px;
    }
    
    .status-error {
      animation: shake 0.5s ease;
      color: #ff6b6b !important;
      border-top: 1px solid rgba(255, 107, 107, 0.3);
      padding-top: 8px;
      margin-top: 8px;
    }
    
    .status-thinking {
      color: #e6e6e6;
      font-size: 13px;
      line-height: 1.6;
      white-space: pre-wrap;
    }
    
    .typing-content {
      position: relative;
      display: block;
    }
    
    .typing-content::after {
      content: '|';
      position: absolute;
      right: -2px;
      animation: blink-caret 0.75s step-end infinite;
    }
    
    /* 添加分隔线样式 */
    .resume-separator {
      height: 1px;
      background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
      margin: 12px 0;
    }
    
    /* 添加最小化/最大化动画 */
    .ai-analysis-status.minimized {
      height: auto !important;
      max-height: 40px !important;
      overflow: hidden;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes fadeInLeft {
      from { opacity: 0; transform: translateX(-10px); }
      to { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      20%, 60% { transform: translateX(-5px); }
      40%, 80% { transform: translateX(5px); }
    }
    
    @keyframes blink-caret {
      from, to { color: transparent; }
      50% { color: #4a90e2; }
    }
  `;
  
  document.head.appendChild(style);
}

// 修改初始化代码，添加样式注入
// 这段代码应该放在页面加载或者扩展初始化的地方
function initializeResumeAnalysis() {
  // 注入样式
  injectAnalysisStyles();
  
  // 其他初始化代码...
}

// 在文件结尾调用初始化
document.addEventListener('DOMContentLoaded', initializeResumeAnalysis);

// 确保初始化被调用
initializeResumeAnalysis();

// 下载HTML结果函数
function downloadResults() {
  console.log('开始下载HTML结果');
  chrome.storage.sync.get(['scoreThreshold'], ({ scoreThreshold = 80 }) => {
    // 对分析结果进行排序（按分数从高到低）
    const sortedResults = [...window.resumeManager.analysisResults].sort((a, b) => {
      const scoreA = a.evaluation?.score || 0;
      const scoreB = b.evaluation?.score || 0;
      return scoreB - scoreA;
    });

    // 准备HTML内容
    const htmlContent = sortedResults.map(result => {
      const latestWork = result.workExperiences?.[0] || {};
      const score = result.evaluation?.score || 0;
      const isQualified = score >= scoreThreshold;
      
      return `
        <div class="resume-item">
          <div class="resume-header">
            <div class="resume-name">${result.name || '未知'}</div>
            <div class="resume-gender">${result.gender || '未知'}</div>
            <div class="resume-age">${result.age || '未知'}</div>
            <div class="resume-experience">${result.experience || '未知'}</div>
            <div class="resume-education">${result.education || '未知'}</div>
            <div class="resume-job-status">${result.jobStatus || '未知'}</div>
          </div>
          <div class="resume-expect">
            <div class="resume-expect-location">${result.expectInfo?.location || '未知'}</div>
            <div class="resume-expect-position">${result.expectInfo?.position || '未知'}</div>
            <div class="resume-expect-salary">${result.expectInfo?.salary || '未知'}</div>
          </div>
          <div class="resume-latest-work">
            <div class="resume-latest-company">${latestWork.company || '未知'}</div>
            <div class="resume-latest-position">${latestWork.position || '未知'}</div>
          </div>
          <div class="resume-evaluation">
            <div class="resume-score">${score}</div>
            <div class="resume-is-qualified">${isQualified ? '是' : '否'}</div>
            <div class="resume-suggestion">${result.evaluation?.suggestion || '无'}</div>
          </div>
          <div class="resume-share">
            <a href="${result.shareLink || '#'}" target="_blank">${result.shareLink || '无'}</a>
          </div>
          <div class="resume-timestamp">${new Date(result.timestamp).toLocaleString()}</div>
          <div class="resume-thinking-process">${result.evaluation?.thinkingProcess || '无记录'}</div>
        </div>
        <div class="resume-separator"></div>
      `;
    }).join('');

    // 生成HTML文件
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `AI简历分析结果_${new Date().toLocaleDateString()}.html`;
    
    console.log('触发HTML下载');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  });
}

// 确保生成唯一的简历ID
function generateUniqueResumeId(resumeData) {
  // 使用多个字段组合生成唯一标识
  const nameStr = resumeData.name || '';
  const genderStr = resumeData.gender || '';
  const expStr = resumeData.experience || '';
  const eduStr = resumeData.education || '';
  
  // 使用部分工作经历信息增加唯一性
  let workStr = '';
  if (resumeData.workExperiences && resumeData.workExperiences.length > 0) {
    const latestWork = resumeData.workExperiences[0];
    workStr = `${latestWork.company || ''}_${latestWork.position || ''}`;
  }
  
  // 结合时间戳创建唯一ID
  const uniqueStr = `${nameStr}_${genderStr}_${expStr}_${eduStr}_${workStr}_${Date.now()}`;
  return btoa(encodeURIComponent(uniqueStr)).replace(/[^a-zA-Z0-9]/g, '');
}

// 检查是否为重复简历
function isDuplicateResume(newResume) {
  // 已分析的结果
  const existingResults = window.resumeManager.analysisResults || [];
  
  for (const existingResume of existingResults) {
    // 比对关键字段
    if (
      newResume.name === existingResume.name &&
      newResume.gender === existingResume.gender &&
      newResume.experience === existingResume.experience &&
      newResume.education === existingResume.education
    ) {
      // 进一步比对工作经历第一项
      const newWork = newResume.workExperiences?.[0] || {};
      const existingWork = existingResume.workExperiences?.[0] || {};
      
      if (newWork.company === existingWork.company && 
          newWork.position === existingWork.position) {
        console.log('检测到重复简历:', newResume.name);
        return true;
      }
    }
  }
  
  return false;
}

// 等待任意一个卡片列表出现
async function waitForAnyCardList(iframeDoc, maxWaitTime = 10000) {
  console.log('等待任意卡片列表加载...');
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    // 检查两种可能的卡片列表选择器
    const cardList = iframeDoc.querySelector('#recommend-list .card-list');
    const recommendCardList = iframeDoc.querySelector('#recommend-list .recommend-card-list');
    
    if (cardList || recommendCardList) {
      const foundElement = cardList || recommendCardList;
      console.log('找到卡片列表:', foundElement.className);
      return foundElement;
    }
    
    // 等待100ms后再次检查
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.warn('等待卡片列表超时');
  throw new Error('等待卡片列表超时 - 未找到 .card-list 或 .recommend-card-list');
}

// 提取新版卡片(.new-geek-wrap)内的基本信息
function extractNewGeekCardInfo(cardInner) {
  try {
    console.log('提取新版简历卡片信息');
    
    if (!cardInner || !cardInner.classList.contains('new-geek-wrap')) {
      console.warn('传入的元素不是有效的新版简历卡片');
      return { resumeId: null };
    }
    
    // 获取卡片中的基本数据
    const result = {
      // 从data-geek属性提取唯一ID
      resumeId: cardInner.getAttribute('data-geek') || null,
      
      // 姓名
      name: cardInner.querySelector('.name')?.textContent?.trim() || '未知',
      
      // 性别 (查找男性或女性图标)
      gender: cardInner.querySelector('.iboss-icon_man') ? '男' : 
              cardInner.querySelector('.iboss-icon_women') ? '女' : '未知',
      
      // 活跃状态
      activeStatus: cardInner.querySelector('.active-text')?.textContent?.trim() || '',
      
      // 薪资期望
      salary: cardInner.querySelector('.salary-wrap')?.textContent?.trim() || '',
      
      // 从第一行基本信息中提取年龄、工作经验、学历和求职状态
      baseInfo: {},
      
      // 期望信息
      expectInfo: {
        location: '',
        position: '',
        salary: ''
      },
      
      // 当前/最近工作
      currentWork: {
        company: '',
        position: '',
        period: ''
      },
      
      // 工作经历列表
      workHistory: [],
      
      // 教育经历
      education: {
        school: '',
        major: '',
        degree: '',
        period: ''
      }
    };
    
    // 解析基本信息行(年龄、工作经验、学历、求职状态)
    const baseInfoText = cardInner.querySelector('.base-info')?.textContent?.trim() || '';
    const baseInfoParts = baseInfoText.split(/·|\s*-\s*|\s+/).filter(item => item && item !== '离职' && !item.includes('line'));
    
    if (baseInfoParts.length >= 1) result.age = baseInfoParts[0];
    if (baseInfoParts.length >= 2) result.experience = baseInfoParts[1];
    if (baseInfoParts.length >= 3) result.education = baseInfoParts[2];
    if (baseInfoParts.length >= 4) result.jobStatus = baseInfoParts.slice(3).join('-');
    
    // 解析期望信息
    const expectRow = cardInner.querySelector('.row-flex');
    if (expectRow) {
      const expectContent = expectRow.querySelector('.content')?.textContent?.trim() || '';
      const expectParts = expectContent.split(/·|\s+/).filter(item => item && !item.includes('dot'));
      
      if (expectParts.length >= 1) result.expectInfo.location = expectParts[0];
      if (expectParts.length >= 2) result.expectInfo.position = expectParts[1];
      
      // 已经从卡片顶部获取了薪资期望
      result.expectInfo.salary = result.salary;
    }
    
    // 解析当前工作信息
    const latelyWork = cardInner.querySelector('.lately-work');
    if (latelyWork) {
      const workContent = latelyWork.querySelector('.content')?.textContent?.trim() || '';
      const workParts = workContent.split(/·|\s+/).filter(item => item && !item.includes('dot'));
      
      if (workParts.length >= 1) result.currentWork.company = workParts[0];
      if (workParts.length >= 2) result.currentWork.position = workParts[1];
      
      // 尝试获取时间段
      const timeItems = cardInner.querySelectorAll('.work-exps .timeline-item');
      if (timeItems && timeItems.length > 0) {
        const firstTimeItem = timeItems[0];
        result.currentWork.period = firstTimeItem.querySelector('.time')?.textContent?.trim() || '';
      }
    }
    
    // 解析工作经历
    const workItems = cardInner.querySelectorAll('.work-exps .timeline-item');
    if (workItems && workItems.length > 0) {
      Array.from(workItems).forEach(item => {
        const timePeriod = item.querySelector('.time')?.textContent?.trim() || '';
        const contentText = item.querySelector('.content')?.textContent?.trim() || '';
        const contentParts = contentText.split(/·|\s+/).filter(item => item && !item.includes('dot'));
        
        const workItem = {
          period: timePeriod,
          company: contentParts[0] || '',
          position: contentParts[1] || ''
        };
        
        result.workHistory.push(workItem);
      });
    }
    
    // 解析教育经历
    const eduItems = cardInner.querySelectorAll('.edu-exps .timeline-item');
    if (eduItems && eduItems.length > 0) {
      const firstEduItem = eduItems[0];
      const timePeriod = firstEduItem.querySelector('.time')?.textContent?.trim() || '';
      const contentText = firstEduItem.querySelector('.content')?.textContent?.trim() || '';
      const contentParts = contentText.split(/·|\s+/).filter(item => item && !item.includes('dot'));
      
      if (contentParts.length >= 1) result.education.school = contentParts[0] || '';
      if (contentParts.length >= 2) result.education.major = contentParts[1] || '';
      if (contentParts.length >= 3) result.education.degree = contentParts[2] || '';
      result.education.period = timePeriod;
    }
    
    // 如果没有提取到resumeId，生成一个基于可用信息的ID
    if (!result.resumeId) {
      const idBase = `${result.name}_${result.gender}_${result.experience}_${result.education}_${result.currentWork.company}`;
      result.resumeId = window.btoa(encodeURIComponent(idBase)).replace(/[^a-zA-Z0-9]/g, '');
    }
    
    console.log('提取的新版简历卡片信息:', result);
    return result;
    
  } catch (error) {
    console.error('提取新版简历卡片信息出错:', error);
    return { resumeId: null };
  }
}

async function extractNewRecommendPageResumeContent(doc, resumeId) {
  try {
    // 等待简历详情加载完成（最多等待10秒）
    const startTime = Date.now();
    let resumeDetail = null;
    
    while (!resumeDetail && Date.now() - startTime < 10000) {
      try {
        resumeDetail = doc.querySelector(".resume-item-content");
      } catch (e) {
        console.warn('查找简历详情元素时出错:', e);
      }
      
      if (!resumeDetail) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    if (!resumeDetail) {
      console.warn('无法获取简历数据，返回空对象');
      return {
        resumeId,
        name: '',
        gender: '',
        age: null,
        experience: '',
        education: '',
        jobStatus: '',
        expectInfo: { location: '', position: '', salary: '', industry: '' },
        workExperiences: [],
        projectExperiences: [],
        educationInfo: { school: '', major: '', degree: '', period: '', experience: '' },
        selfDescription: '',
        timestamp: new Date().toISOString()
      };
    }

    // 提取基本信息
    let baseInfo = {};
    try {
      const baseInfoItem = resumeDetail.querySelector('.resume-item.item-base');
      if (baseInfoItem) {
        // 提取姓名和性别
        const nameElement = baseInfoItem.querySelector('.geek-name');
        const name = nameElement ? nameElement.textContent.trim() : '';
        
        // 判断性别
        const genderMale = baseInfoItem.querySelector('[class^="iboss-icon_man"]'); // 以iboss-icon_man开头的类
        const genderFemale = baseInfoItem.querySelector('[class^="iboss-icon_women"]'); // 以iboss-icon_women开头的类
        const gender = genderMale ? '男' : (genderFemale ? '女' : '');
        
        // 提取活跃状态
        const activeElement = baseInfoItem.querySelector('.text-orange');
        const activeStatus = activeElement ? activeElement.textContent.trim() : '';
        
        // 提取自我描述
        const descElement = baseInfoItem.querySelector('.text.selfDescription');
        const selfDescription = descElement ? descElement.textContent.trim() : '';
        
        baseInfo = {
          name,
          gender,
          isElite: false, // 在新DOM中可能需要查找精英标识
          activeStatus,
          selfDescription
        };
        
        console.log('提取基本信息成功:', baseInfo);
      }
    } catch (e) {
      console.warn('提取基本信息时出错:', e);
      baseInfo = {
        name: '',
        gender: '',
        isElite: false,
        activeStatus: '',
        selfDescription: ''
      };
    }

    // 提取年龄、经验、学历、求职状态
    let age = null, experience = '', education = '', jobStatus = '';
    try {
      const infoLabels = resumeDetail.querySelectorAll('.info-labels .label-text');
      if (infoLabels && infoLabels.length > 0) {
        for (let i = 0; i < infoLabels.length; i++) {
          const labelText = infoLabels[i].textContent.trim();
          
          // 提取年龄
          if (i === 0 && labelText.includes('岁')) {
            const ageMatch = labelText.match(/(\d+)岁/);
            if (ageMatch) {
              age = parseInt(ageMatch[1]);
            }
          }
          
          // 提取工作经验
          if (i === 1) {
            experience = labelText.trim();
          }
          
          // 提取学历
          if (i === 2) {
            education = labelText.trim();
          }
          
          // 提取求职状态
          if (i === 3) {
            jobStatus = labelText.trim();
          }
        }
      }
    } catch (e) {
      console.warn('提取标签信息时出错:', e);
    }

    // 提取岗位经验
    let positionExperience = '';
    try {
      const stationItem = resumeDetail.querySelector('.resume-item.resume-station');
      if (stationItem) {
        const expTag = stationItem.querySelector('.tags span');
        if (expTag) {
          positionExperience = expTag.textContent.trim();
        }
      }
    } catch (e) {
      console.warn('提取岗位经验时出错:', e);
    }

    // 提取期望职位信息
    let expectInfo = { location: '', position: '', salary: '', industry: '' };
    try {
      const expectItem = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(
        item => item.querySelector('h3.title') && item.querySelector('h3.title').textContent.trim() === '期望职位'
      );
      
      if (expectItem) {
        const expectLabels = expectItem.querySelectorAll('.info-labels .label-text');
        if (expectLabels.length >= 1) expectInfo.location = expectLabels[0].textContent.trim();
        if (expectLabels.length >= 2) expectInfo.position = expectLabels[1].textContent.trim();
        if (expectLabels.length >= 3) expectInfo.industry = expectLabels[2].textContent.trim();
        if (expectLabels.length >= 4) expectInfo.salary = expectLabels[3].textContent.trim();
      }
      console.log('提取期望职位信息成功:', expectInfo);
    } catch (error) {
      console.warn('提取期望职位信息失败:', error);
    }

    // 提取工作经历
    let workExperiences = [];
    try {
      const workItem = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(
        item => item.querySelector('h3.title') && item.querySelector('h3.title').textContent.trim() === '工作经历'
      );
      
      if (workItem) {
        const historyItems = workItem.querySelectorAll('.history-item');
        workExperiences = Array.from(historyItems).map(item => {
          try {
            // 获取工作时间段
            const period = item.querySelector('.period')?.textContent?.trim() || '';
            
            // 获取公司名称和职位
            const nameElement = item.querySelector('h4.name');
            let company = '', position = '', department = '';
            
            if (nameElement) {
              const companyElement = nameElement.querySelector('.helper-text');
              if (companyElement) {
                company = companyElement.textContent.trim();
              }
              
              // 获取职位信息
              const positionSpan = nameElement.querySelector('span');
              if (positionSpan) {
                position = positionSpan.textContent.trim();
              }
            }
            
            // 获取工作内容描述
            const contentElement = item.querySelector('.item-text .text');
            const content = contentElement ? contentElement.textContent.trim() : '';
            
            // 获取技能标签
            const tagElements = item.querySelectorAll('.tags span');
            const skills = Array.from(tagElements).map(tag => tag.textContent.trim()).filter(Boolean);
            
            // 判断是否为实习经历
            const isInternship = false; // 新DOM中可能需要其他方式判断
            
            return {
              company,
              position,
              department,
              period,
              content,
              isInternship,
              skills
            };
          } catch (e) {
            console.warn('提取单个工作经历时出错:', e);
            return null;
          }
        }).filter(Boolean);
      }
      console.log(`提取工作经历成功，共${workExperiences.length}条`);
    } catch (error) {
      console.warn('提取工作经历失败:', error);
    }

    // 提取教育经历
    let educationInfo = { school: '', major: '', degree: '', period: '', experience: '' };
    try {
      const eduItem = Array.from(resumeDetail.querySelectorAll('.resume-item')).find(
        item => item.querySelector('h3.title') && item.querySelector('h3.title').textContent.trim() === '教育经历'
      );
      
      if (eduItem) {
        const historyItem = eduItem.querySelector('.history-item');
        if (historyItem) {
          // 获取学习时间段
          const periodElement = historyItem.querySelector('.period');
          const period = periodElement ? periodElement.textContent.trim() : '';
          
          // 获取学校、专业和学位
          const schoolNameElement = historyItem.querySelector('.school-info .name b');
          const school = schoolNameElement ? schoolNameElement.textContent.trim() : '';
          
          const majorElement = historyItem.querySelector('.school-info .name .major');
          const major = majorElement ? majorElement.textContent.trim() : '';
          
          // 获取学位
          const degreeText = historyItem.querySelector('.school-info .name')?.textContent || '';
          let degree = '';
          if (degreeText.includes('本科')) {
            degree = '本科';
          } else if (degreeText.includes('硕士')) {
            degree = '硕士';
          } else if (degreeText.includes('博士')) {
            degree = '博士';
          } else if (degreeText.includes('专科')) {
            degree = '专科';
          }
          
          // 获取在校经历
          const expContent = historyItem.querySelector('.school-item .content');
          const experience = expContent ? expContent.textContent.trim() : '';
          
          educationInfo = {
            school,
            major,
            degree,
            period,
            experience
          };
        }
      }
      console.log('提取教育经历成功:', educationInfo);
    } catch (error) {
      console.warn('提取教育经历失败:', error);
    }

    // 提取社团经历（作为项目经验的一部分）
    let projectExperiences = [];
    try {
      const clubSection = resumeDetail.querySelector('.resume-section.geek-club-experience-wrap');
      if (clubSection) {
        const clubWraps = clubSection.querySelectorAll('.club-wrap');
        projectExperiences = Array.from(clubWraps).map(club => {
          try {
            const nameWrap = club.querySelector('.name-wrap');
            let name = '', role = '';
            
            if (nameWrap) {
              const nameText = nameWrap.querySelector('.name')?.textContent || '';
              const nameParts = nameText.split(/\s*\n\s*/).map(p => p.trim()).filter(Boolean);
              if (nameParts.length >= 1) name = nameParts[0];
              if (nameParts.length >= 2) role = nameParts[1];
            }
            
            const period = nameWrap?.querySelector('.period')?.textContent?.trim() || '';
            const content = club.querySelector('.item-content')?.textContent?.trim() || '';
            
            return {
              name,
              role,
              period,
              content,
              performance: '' // 社团经历中通常没有业绩描述
            };
          } catch (e) {
            console.warn('提取单个社团经历时出错:', e);
            return null;
          }
        }).filter(Boolean);
      }
      console.log(`提取社团经历成功，共${projectExperiences.length}条`);
    } catch (error) {
      console.warn('提取社团经历失败:', error);
    }

    // 组装最终结果
    const content = {
      resumeId,
      ...baseInfo,
      age,
      experience: positionExperience || experience,
      education: educationInfo.degree || education,
      jobStatus,
      expectInfo,
      workExperiences,
      projectExperiences,
      educationInfo,
      selfDescription: baseInfo.selfDescription || '',
      timestamp: new Date().toISOString()
    };
    
    console.log('成功提取简历内容:', content);
    return content;
  } catch (error) {
    console.error('提取简历内容时发生错误:', error);
    // 即使发生错误，也返回一个包含基本信息的对象
    return {
      resumeId,
      name: '',
      gender: '',
      age: null,
      experience: '',
      education: '',
      jobStatus: '',
      expectInfo: { location: '', position: '', salary: '', industry: '' },
      workExperiences: [],
      projectExperiences: [],
      educationInfo: { school: '', major: '', degree: '', period: '', experience: '' },
      selfDescription: '',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

// 提取简历iframe中的Canvas内容
async function extractResumeCanvas(doc = document) {
  try {
    console.log('开始提取简历Canvas内容');
    
    // 查找简历iframe，使用更精确的选择器
    const iframe = doc.querySelector('iframe[src^="/web/frame/recommend/"]');
    if (!iframe) {
      console.error('未找到推荐页面iframe');
      return null;
    }
    console.log('找到推荐页面iframe:', iframe.src);

    // 创建一个Promise来等待iframe中的脚本发送文本内容
    console.log('设置文本提取Promise');
    const textPromise = new Promise((resolve, reject) => {
      let retryCount = 0;
      const maxRetries = 3; // 减少重试次数
      const retryTimeout = 10000; // 减少每次重试等待时间到10秒
      let isResolved = false;
      let messageTimeoutId = null;

      function startRetryTimeout() {
        return setTimeout(() => {
          if (isResolved) return;
          
          if (retryCount < maxRetries) {
            console.log(`第${retryCount + 1}次重试提取文本`);
            retryCount++;
            // 重新注入脚本并开始新的超时计时
            injectScriptToRecommendFrame();
            messageTimeoutId = startRetryTimeout();
          } else {
            console.error('多次重试后仍未收到有效文本，返回空字符串');
            isResolved = true;
            if (textReject) {
              textReject(new Error('等待文本提取超时'));
              textResolve = null;
              textReject = null;
            }
          }
        }, retryTimeout);
      }

      // 注入脚本到推荐页面iframe - 优化注入逻辑
      const injectScriptToRecommendFrame = async () => {
        if (!iframe.contentWindow) {
          console.error('无法访问推荐页面iframe的contentWindow');
          return;
        }

        try {
          // 等待iframe加载完成 - 减少等待时间
          if (!iframe.contentDocument || iframe.contentDocument.readyState !== 'complete') {
            await new Promise((resolve) => {
              const timeout = setTimeout(resolve, 3000); // 3秒超时
              iframe.addEventListener('load', () => {
                clearTimeout(timeout);
                resolve();
              }, { once: true });
            });
          }

          // 查找简历iframe
          const resumeIframe = iframe.contentDocument.querySelector('iframe[src*="/web/frame/c-resume/"]');
          if (!resumeIframe) {
            console.error('未找到简历iframe');
            return;
          }
          console.log('找到简历iframe:', resumeIframe.src);

          // 等待简历iframe加载完成 - 减少等待时间
          if (!resumeIframe.contentDocument || resumeIframe.contentDocument.readyState !== 'complete') {
            await new Promise((resolve) => {
              const timeout = setTimeout(resolve, 3000); // 3秒超时
              resumeIframe.addEventListener('load', () => {
                clearTimeout(timeout);
                resolve();
              }, { once: true });
            });
          }

          // 检查是否已经注入了中继脚本到推荐页面 - 使用更高效的检查
          const existingRelayScript = iframe.contentDocument.querySelector('script[src*="relay-script.js"]');
          if (!existingRelayScript) {
            // 注入中继脚本到推荐页面
            const relayScript = document.createElement('script');
            relayScript.src = chrome.runtime.getURL('relay-script.js');
            iframe.contentDocument.head.appendChild(relayScript);
            console.log('已注入中继脚本到推荐页面iframe');
          } else {
            console.log('中继脚本已存在，跳过注入');
          }

          // 检查是否已经注入了简历脚本到简历iframe - 使用更高效的检查
          const existingResumeScript = resumeIframe.contentDocument.querySelector('script[src*="resume-frame-script.js"]');
          if (!existingResumeScript) {
            // 注入简历脚本到简历iframe
            const resumeScript = document.createElement('script');
            resumeScript.src = chrome.runtime.getURL('resume-frame-script.js');
            resumeIframe.contentDocument.head.appendChild(resumeScript);
            console.log('已注入简历脚本到简历iframe');
          } else {
            console.log('简历脚本已存在，跳过注入');
          }

        } catch (e) {
          console.error('注入脚本时出错:', e);
        }
      };

      // 保存resolve和reject函数以供全局消息处理器使用
      textResolve = (text) => {
        if (!isResolved) {
          isResolved = true;
          if (messageTimeoutId) {
            clearTimeout(messageTimeoutId);
            messageTimeoutId = null;
          }
          resolve(text);
        }
      };
      textReject = (error) => {
        if (!isResolved) {
          isResolved = true;
          if (messageTimeoutId) {
            clearTimeout(messageTimeoutId);
            messageTimeoutId = null;
          }
          reject(error);
        }
      };

      // 注入脚本
      injectScriptToRecommendFrame();

      // 启动第一次超时计时
      messageTimeoutId = startRetryTimeout();
    });

    const text = await textPromise;
    console.log('成功提取文本，长度:', text ? text.length : 0);
    return text || ''; // 确保返回字符串
  } catch (error) {
    console.error('提取Canvas内容时出错:', error);
    return ''; // 返回空字符串而不是抛出错误
  } finally {
    // 清理资源
    if (messageTimeoutId) {
      clearTimeout(messageTimeoutId);
      messageTimeoutId = null;
    }
    textResolve = null;
    textReject = null;
  }
}
// 新增：带重试的Canvas等待函数
async function waitForCanvasWithRetry(iframeDoc, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const canvas = await waitForCanvasInIframe(iframeDoc);
      if (canvas) return canvas;
    } catch (e) {
      console.warn(`第${attempt}次等待Canvas失败:`, e);
      if (attempt === maxAttempts) throw e;
      await new Promise(resolve => setTimeout(resolve, attempt * 1000));
    }
  }
  return null;
}

// 新增：从DOM提取文本的函数
async function extractTextFromDOM(iframeDoc) {
  const resumeElement = iframeDoc.querySelector('#resume');
  if (!resumeElement) return null;
  
  // 获取所有文本节点
  const textNodes = [];
  const walker = document.createTreeWalker(
    resumeElement,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );

  let node;
  while (node = walker.nextNode()) {
    const text = node.textContent.trim();
    if (text) textNodes.push(text);
  }

  return textNodes.join('\n');
}

// 截图并裁剪简历区域
async function captureAndCropResume(iframeRect, resumeRect) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      action: 'captureVisibleTab'
    }, response => {
      if (!response || !response.success) {
        return reject(new Error('截图失败'));
      }
      
      // 创建一个新的图像对象
      const img = new Image();
      img.onload = function() {
        try {
          // 创建Canvas用于裁剪
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          // 计算简历在整个页面中的位置
          const x = iframeRect.left + resumeRect.left;
          const y = iframeRect.top + resumeRect.top;
          const width = resumeRect.width;
          const height = resumeRect.height;
          
          // 设置Canvas尺寸
          canvas.width = width;
          canvas.height = height;
          
          // 裁剪图像
          ctx.drawImage(img, x, y, width, height, 0, 0, width, height);
          
          // 获取裁剪后的图像数据
          const croppedDataURL = canvas.toDataURL('image/png');
          resolve(croppedDataURL);
        } catch (e) {
          reject(e);
        }
      };
      
      img.onerror = function() {
        reject(new Error('加载截图失败'));
      };
      
      // 设置图像源
      img.src = response.imageData;
    });
  });
}

// 调用OCR服务识别Canvas中的文本
async function recognizeCanvasText(imageData) {
  try {
    const apiKey = await getApiKey();
    const environment = await getCurrentEnvironment();
    
    if (!apiKey) {
      console.error('未设置API密钥，无法进行OCR识别');
      return null;
    }
    
    const baseUrl = window.API_CONFIG[environment].baseUrl;
    console.log('调用OCR服务...');

    // 创建 AbortController 用于超时控制
    const controller = new AbortController();
    const timeout = setTimeout(() => {
      controller.abort();
    }, 30000); // 30秒超时

    try {
      // 调用后端OCR服务
      const base_url = window.API_CONFIG[environment].baseUrl;
      const response = await fetch(`${base_url}/api/ocr`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          image: imageData.split(',')[1] // 移除data:image/png;base64,前缀
        }),
        signal: controller.signal // 添加 signal 用于超时控制
      });
      
      clearTimeout(timeout); // 清除超时定时器

      if (!response.ok) {
        throw new Error(`OCR请求失败: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('OCR识别成功');
      return result;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('OCR请求超时（30秒）');
      }
      throw error;
    } finally {
      clearTimeout(timeout); // 确保定时器被清除
    }
  } catch (error) {
    console.error('OCR识别出错:', error);
    return null;
  }
}

// 在iframe文档加载完成后使用MutationObserver监听DOM变化
async function waitForCanvasInIframe(iframeDoc) {
  console.log('开始监听iframe内容变化，等待canvas出现...');
  
  return new Promise((resolve, reject) => {
    // 先检查一次是否已存在
    let canvas = iframeDoc.querySelector('canvas');
    if (canvas) {
      console.log('立即找到canvas元素');
      return resolve(canvas);
    }
    
    // 设置监听器
    const observer = new MutationObserver((mutations) => {
      canvas = iframeDoc.querySelector('canvas');
      if (canvas) {
        console.log('通过MutationObserver发现canvas元素');
        observer.disconnect();
        resolve(canvas);
      }
    });
    
    // 开始监听
    observer.observe(iframeDoc.documentElement, {
      childList: true,
      subtree: true
    });
    
    // 设置超时
    setTimeout(() => {
      observer.disconnect();
      reject(new Error('等待canvas超时(10秒)'));
    }, 10000);
  });
}

// 修改extractResumeCanvas函数，在获取到iframeDoc后

// 在iframe加载完成后延迟执行，让JavaScript有时间渲染canvas
async function delayedCanvasSearch(iframeDoc) {
  // 先等待一段时间让iframe内的JS执行完成
  console.log('延迟查询canvas，等待JavaScript渲染...');
  
  // 多次尝试，每次间隔增加
  for (let attempt = 1; attempt <= 5; attempt++) {
    await new Promise(resolve => setTimeout(resolve, attempt * 1000));
    
    console.log(`第${attempt}次尝试查找canvas...`);
    const allCanvas = iframeDoc.querySelectorAll('canvas');
    console.log(`找到${allCanvas.length}个canvas元素`);
    
    if (allCanvas.length > 0) {
      return allCanvas[0];
    }
  }
  
  throw new Error('多次尝试后仍未找到canvas元素');
}

// 检查iframe是否完全加载
function checkIframeContent(iframeDoc) {
  console.log('检查iframe内容:');
  console.log('- body存在:', !!iframeDoc.body);
  console.log('- HTML内容长度:', iframeDoc.documentElement.innerHTML.length);
  console.log('- 所有div元素数量:', iframeDoc.querySelectorAll('div').length);
  console.log('- 显示iframe body的子元素:');
  
  if (iframeDoc.body) {
    Array.from(iframeDoc.body.children).forEach((el, i) => {
      console.log(`  子元素${i}: ${el.tagName}, id=${el.id}, class=${el.className}`);
    });
  }
  
  // 特别检查id为resume的div，这应该是canvas的父元素
  const resumeDiv = iframeDoc.querySelector('div#resume');
  console.log('- id为resume的div:', resumeDiv ? '存在' : '不存在');
  if (resumeDiv) {
    console.log('  resume div内部元素:');
    Array.from(resumeDiv.children).forEach((el, i) => {
      console.log(`  子元素${i}: ${el.tagName}, id=${el.id}`);
    });
  }
}

// 使用脚本注入方式获取Canvas
async function getCanvasViaScriptInjection() {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      action: 'captureCanvas',
      tabId: window.tabId
    }, response => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else if (response.success) {
        resolve(response.dataUrl);
      } else {
        reject(new Error(response.error || '获取Canvas失败'));
      }
    });
  });
}

// 等待一定时间让WebAssembly加载完成
async function waitForWebAssemblyLoad(iframeDoc, maxAttempts = 10) {
  console.log('等待WebAssembly加载和渲染...');
  
  for (let i = 1; i <= maxAttempts; i++) {
    console.log(`等待尝试 ${i}/${maxAttempts}...`);
    
    // 等待递增的时间
    await new Promise(resolve => setTimeout(resolve, i * 500));
    
    // 获取canvas并检查其状态
    const canvas = iframeDoc.querySelector('canvas');
    
    if (canvas) {
      // 如果canvas尺寸不是默认的300x150，可能已经渲染完成
      if (canvas.width !== 300 || canvas.height !== 150) {
        console.log(`Canvas尺寸变化: ${canvas.width}x${canvas.height}，认为已渲染完成`);
        return canvas;
      }
      
      // 尝试获取像素数据检查是否已渲染
      try {
        const ctx = canvas.getContext('2d');
        const data = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        let hasContent = false;
        
        // 检查像素数据是否都是0（透明）
        for (let i = 0; i < data.length; i += 4) {
          if (data[i] !== 0 || data[i+1] !== 0 || data[i+2] !== 0 || data[i+3] !== 0) {
            hasContent = true;
            break;
          }
        }
        
        if (hasContent) {
          console.log('Canvas已包含像素数据，认为已渲染完成');
          return canvas;
        }
      } catch (e) {
        console.log('检查Canvas内容失败:', e);
      }
    }
  }
  
  console.warn('等待WebAssembly加载超时，将使用当前状态的canvas');
  return iframeDoc.querySelector('canvas');
}

// background.js中添加截图功能
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'captureVisibleTab') {
    chrome.tabs.captureVisibleTab(null, {format: 'png'}, imageData => {
      sendResponse({success: true, imageData});
    });
    return true;
  }
});

// 在resume.js中
async function capturePageAndCropIframe() {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      action: 'captureVisibleTab'
    }, response => {
      if (response && response.success) {
        // 这里需要添加裁剪逻辑，根据iframe的位置和尺寸
        resolve(response.imageData);
      } else {
        reject(new Error('截图失败'));
      }
    });
  });
}

// 强制在iframe中注入处理WebAssembly错误的代码
async function handleWebAssemblyError(iframeDoc) {
  try {
    const script = iframeDoc.createElement('script');
    script.textContent = `
      // 捕获并处理WebAssembly错误
      window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('WebAssembly')) {
          console.log('捕获到WebAssembly错误，尝试修复...');
          
          // 等待一段时间后尝试重新初始化
          setTimeout(() => {
            // 触发可能重新初始化WebAssembly的操作
            const event = new Event('resize');
            window.dispatchEvent(event);
          }, 1000);
        }
      });
    `;
    iframeDoc.head.appendChild(script);
    console.log('已注入WebAssembly错误处理代码');
  } catch (e) {
    console.error('注入错误处理代码失败:', e);
  }
}


// 添加此函数来注入Canvas捕获脚本
async function injectCanvasCaptureScript(iframeDoc) {
  try {
    console.log('开始注入Canvas捕获脚本...');
    
    // 创建脚本元素
    const script = iframeDoc.createElement('script');
    script.textContent = `
    (function() {
      'use strict';
    
      console.log("Canvas Capture Extension: Content script injected.");
    
      const drawingOperations = []; // 存储 Canvas 绘图操作的数组
      const methodsToHook = [
        'fillRect', 'strokeRect', 'fillText', 'strokeText', 'beginPath', 
        'moveTo', 'lineTo', 'closePath', 'fill', 'stroke', 'drawImage', 'clearRect',
        // ...其他方法...
      ];
    
      // 检查 CanvasRenderingContext2D 是否存在
      if (typeof CanvasRenderingContext2D !== 'undefined') {
        methodsToHook.forEach(methodName => {
          if (CanvasRenderingContext2D.prototype[methodName]) {
            const originalMethod = CanvasRenderingContext2D.prototype[methodName];
    
            CanvasRenderingContext2D.prototype[methodName] = function(...args) {
              // 特别注意fillText方法，它包含显示的文本
              if (methodName === 'fillText' || methodName === 'strokeText') {
                console.log('Canvas文本内容:', args[0], '位置:', args[1], args[2]);
              }
              
              drawingOperations.push({
                method: methodName,
                arguments: Array.from(args)
              });
    
              return originalMethod.apply(this, args);
            };
          }
        });
      }
    
      // 将捕获的操作暴露到window对象
      window.getCanvasOperations = function() {
        return drawingOperations;
      };
      
      // 添加提取文本内容的函数
      window.extractCanvasText = function() {
        const textContent = [];
        drawingOperations.forEach(op => {
          if (op.method === 'fillText' || op.method === 'strokeText') {
            if (op.arguments[0] && typeof op.arguments[0] === 'string') {
              textContent.push({
                text: op.arguments[0],
                x: op.arguments[1],
                y: op.arguments[2]
              });
            }
          }
        });
        return textContent;
      };
    
      console.log("Canvas捕获脚本注入完成。使用window.extractCanvasText()获取文本内容。");
    })();
    `;
    
    // 将脚本添加到iframe文档中
    iframeDoc.head.appendChild(script);
    console.log('Canvas捕获脚本注入成功');
    
    // 等待脚本执行完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return true;
  } catch (e) {
    console.error('注入Canvas捕获脚本时出错:', e);
    return false;
  }
}

// 添加函数来获取Canvas中的文本内容
async function extractTextFromCanvasOperations(iframe) {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    const canvas = iframeDoc.querySelector('canvas');
    
    if (!canvas) {
      console.warn('未找到Canvas元素');
      return null;
    }
    
    // 确保extractCanvasText函数存在
    if (typeof iframe.contentWindow.extractCanvasText !== 'function') {
      console.warn('extractCanvasText函数未找到，可能脚本注入失败');
      return null;
    }
    
    // 清理之前的操作记录
    if (typeof iframe.contentWindow.clearCanvasTextOperations === 'function') {
      iframe.contentWindow.clearCanvasTextOperations();
    }
    
    // 等待一段时间确保Canvas完全渲染
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 提取文本操作
    const textOperations = iframe.contentWindow.extractCanvasText(canvas);
    
    if (!textOperations || textOperations.length === 0) {
      console.warn('未找到任何文本操作');
      return null;
    }
    
    console.log(`找到 ${textOperations.length} 个文本操作`);
    
    // 按y坐标分组
    const groupedText = groupTextByPosition(textOperations);
    
    // 格式化文本内容
    const formattedText = formatTextContent(groupedText);
    
    if (formattedText) {
      console.log(`提取的文本长度: ${formattedText.length}`);
      return formattedText;
    }
    
    return null;
  } catch (error) {
    console.error('从Canvas操作提取文本失败:', error);
    return null;
  }
}

function groupTextByPosition(textOperations) {
  // 按y坐标分组，允许5像素的误差
  const groups = {};
  const tolerance = 5;
  
  textOperations.forEach(op => {
    const y = Math.round(op.y / tolerance) * tolerance;
    if (!groups[y]) {
      groups[y] = [];
    }
    groups[y].push(op);
  });
  
  // 对每个组内的文本按x坐标排序
  Object.values(groups).forEach(group => {
    group.sort((a, b) => a.x - b.x);
  });
  
  return groups;
}

function formatTextContent(groupedText) {
  try {
    // 获取所有y坐标并排序
    const yPositions = Object.keys(groupedText).map(Number).sort((a, b) => a - b);
    
    // 合并文本，处理行间距
    let lastY = null;
    let result = '';
    
    yPositions.forEach(y => {
      const line = groupedText[y].map(op => op.text).join(' ').trim();
      if (!line) return;
      
      if (lastY !== null) {
        // 如果行间距大于50像素，添加额外的换行
        const gap = y - lastY;
        if (gap > 50) {
          result += '\n\n';
        } else {
          result += '\n';
        }
      }
      
      result += line;
      lastY = y;
    });
    
    return result.trim();
  } catch (error) {
    console.error('格式化文本内容时出错:', error);
    return null;
  }
}

// 获取元素的xpath
function getXPath(element) {
  // 首先尝试查找包含data-jid的a标签
  const link = element.querySelector('a[data-jid]');
  if (link) {
    const jid = link.getAttribute('data-jid');
    // 使用data-jid属性构建XPath
    return `//a[@data-jid='${jid}']`;
  }
  
  // 如果找不到data-jid，则返回null
  console.warn('未找到data-jid属性，无法生成唯一XPath');
  return null;
}

// 注入增强的Canvas捕获脚本
async function injectEnhancedCanvasCaptureScript(iframeDoc) {
  try {
    console.log('开始注入增强的Canvas捕获脚本...');
    
    // 创建并注入脚本
    const script = iframeDoc.createElement('script');
    script.textContent = `
      (function() {
        // 存储原始的Canvas方法
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        
        // 重写getContext方法
        HTMLCanvasElement.prototype.getContext = function() {
          const context = originalGetContext.apply(this, arguments);
          if (context && !this.enhanced) {
            this.enhanced = true;
            
            // 存储所有文本操作
            if (!window.canvasTextOperations) {
              window.canvasTextOperations = [];
            }
            
            // 拦截fillText和strokeText
            const originalFillText = context.fillText;
            const originalStrokeText = context.strokeText;
            
            context.fillText = function(text, x, y) {
              window.canvasTextOperations.push({
                text,
                x,
                y,
                type: 'fillText',
                style: {
                  font: this.font,
                  fillStyle: this.fillStyle
                }
              });
              return originalFillText.apply(this, arguments);
            };
            
            context.strokeText = function(text, x, y) {
              window.canvasTextOperations.push({
                text,
                x,
                y,
                type: 'strokeText',
                style: {
                  font: this.font,
                  strokeStyle: this.strokeStyle
                }
              });
              return originalStrokeText.apply(this, arguments);
            };
          }
          return context;
        };
        
        // 添加提取文本的方法
        window.extractCanvasText = function(canvas) {
          if (!canvas) {
            console.error('Canvas元素未找到');
            return null;
          }
          
          // 尝试强制重绘以捕获所有文本
          const ctx = canvas.getContext('2d');
          if (ctx) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            ctx.putImageData(imageData, 0, 0);
          }
          
          // 返回捕获的文本操作
          return window.canvasTextOperations || [];
        };
        
        // 添加清理方法
        window.clearCanvasTextOperations = function() {
          window.canvasTextOperations = [];
        };
        
        // 处理WebAssembly MIME类型问题
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
          const response = await originalFetch.apply(this, args);
          if (args[0] && args[0].endsWith('.wasm')) {
            // 创建新的Response对象，设置正确的MIME类型
            return new Response(response.body, {
              headers: {
                'Content-Type': 'application/wasm',
                ...response.headers
              }
            });
          }
          return response;
        };
      })();
    `;
    
    // 注入脚本
    iframeDoc.head.appendChild(script);
    
    // 等待脚本执行
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 验证脚本注入
    const canvasElements = iframeDoc.getElementsByTagName('canvas');
    for (const canvas of canvasElements) {
      canvas.setAttribute('crossorigin', 'anonymous');
      
      // 尝试预加载图像内容
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        ctx.putImageData(imageData, 0, 0);
      }
    }
    
    console.log('增强的Canvas捕获脚本注入成功');
    return true;
  } catch (error) {
    console.error('注入增强的Canvas捕获脚本失败:', error);
    return false;
  }
}

// 优化从Canvas操作中提取文本的函数
async function extractTextFromCanvasOperations(iframe) {
  try {
    const iframeWin = iframe.contentWindow;
    
    // 检查extractCanvasText函数是否已注入
    if (typeof iframeWin.extractCanvasText !== 'function') {
      console.warn('extractCanvasText函数未找到，可能脚本注入失败');
      return null;
    }
    
    // 调用iframe中的函数获取文本内容
    const textItems = await new Promise(resolve => {
      setTimeout(() => {
        const items = iframeWin.extractCanvasText();
        resolve(items);
      }, 500); // 给予一些时间让Canvas完全渲染
    });

    if (!textItems || textItems.length === 0) {
      console.warn('未找到Canvas文本内容');
      return null;
    }

    console.log('从Canvas操作中提取的文本项数量:', textItems.length);
    
    // 将文本项按位置排序并分组
    const groupedText = groupTextByPosition(textItems);
    
    // 合并文本内容
    const fullText = formatTextContent(groupedText);
    
    console.log('提取的完整文本长度:', fullText.length);
    return fullText;
  } catch (e) {
    console.error('从Canvas提取文本失败:', e);
    return null;
  }
}

// 新增：按位置对文本进行分组
function groupTextByPosition(textItems) {
  // 按y坐标分组（考虑一定的误差范围）
  const yThreshold = 5; // y坐标误差范围
  const groups = {};
  
  textItems.forEach(item => {
    // 找到最接近的y坐标组
    const y = Math.round(item.y / yThreshold) * yThreshold;
    if (!groups[y]) {
      groups[y] = [];
    }
    groups[y].push(item);
  });
  
  // 对每个组内的文本按x坐标排序
  Object.values(groups).forEach(group => {
    group.sort((a, b) => a.x - b.x);
  });
  
  return groups;
}

// 新增：格式化文本内容
function formatTextContent(groupedText) {
  // 按y坐标排序
  const sortedY = Object.keys(groupedText).sort((a, b) => Number(a) - Number(b));
  
  let formattedText = '';
  let lastY = null;
  
  sortedY.forEach(y => {
    const group = groupedText[y];
    
    // 计算当前行与上一行的距离
    if (lastY !== null) {
      const yDiff = Number(y) - Number(lastY);
      // 如果行间距较大，添加额外的换行
      if (yDiff > 20) {
        formattedText += '\n';
      }
    }
    
    // 合并同一行的文本
    const lineText = group.map(item => item.text.trim())
      .filter(text => text) // 过滤空文本
      .join(' ');
    
    if (lineText) {
      formattedText += (formattedText ? '\n' : '') + lineText;
    }
    
    lastY = y;
  });
  
  return formattedText;
}

// 全局消息处理
let textResolve = null;
let textReject = null;
let messageTimeoutId = null;
let messageListener = null; // 存储消息监听器引用
let isPageVisible = true; // 页面可见性状态
let lastActivityTime = Date.now(); // 最后活动时间

// 页面可见性变化检测
function handleVisibilityChange() {
  isPageVisible = !document.hidden;
  if (isPageVisible) {
    console.log('页面重新可见，检查是否需要清理资源');
    cleanupStaleResources();
  }
}

// 清理过期资源
function cleanupStaleResources() {
  const now = Date.now();
  const staleThreshold = 30000; // 30秒
  
  // 清理过期的Promise
  if (textResolve && (now - lastActivityTime) > staleThreshold) {
    console.log('清理过期的文本提取Promise');
    if (textReject) {
      textReject(new Error('资源清理：Promise已过期'));
    }
    textResolve = null;
    textReject = null;
  }
  
  // 清理过期的定时器
  if (messageTimeoutId) {
    clearTimeout(messageTimeoutId);
    messageTimeoutId = null;
  }
  
  lastActivityTime = now;
}

// 设置页面可见性监听
document.addEventListener('visibilitychange', handleVisibilityChange);

// 设置全局消息监听器
function setupMessageListener() {
  // 如果已存在监听器，先移除
  if (messageListener) {
    window.removeEventListener('message', messageListener);
  }
  
  messageListener = function handleMessage(event) {
    // 更新最后活动时间
    lastActivityTime = Date.now();
    
    // 忽略来自React DevTools的消息
    if (event.data && event.data.source === 'react-devtools-content-script') {
      return;
    }

    // 打印接收到的所有消息，用于调试
    // console.log('主页面收到消息:', {
    //   type: event.data?.type,
    //   source: event.data?.source,
    //   origin: event.origin,
    //   data: event.data?.data ? {
    //     textLength: event.data.data.textLength,
    //     fromIframe: event.data.data.fromIframe,
    //     relayedFrom: event.data.data.relayedFrom,
    //     relayTimestamp: event.data.data.relayTimestamp,
    //     isComplete: event.data.data.isComplete
    //   } : null
    // });

    // 检查消息是否来自我们的脚本
    if (event.data && 
        event.data.type === 'resumeTextExtracted' && 
        event.data.source === 'resume-frame-script' &&
        event.data.data) {
      
      const { text, fromIframe, relayedFrom, isComplete } = event.data.data;
      
      // 验证消息来源是否是简历iframe
      if (!fromIframe || !fromIframe.includes('/web/frame/c-resume/')) {
        console.log('忽略非简历iframe的消息:', fromIframe);
        return;
      }

      console.log('收到有效的简历文本:', {
        textLength: text ? text.length : 0,
        fromIframe,
        relayedFrom,
        isComplete,
        textPreview: text ? text.substring(0, 100) + '...' : null
      });

      if (text && text.length > 0 && textResolve) {
        clearTimeout(messageTimeoutId);
        textResolve(text);
        textResolve = null;
        textReject = null;
        messageTimeoutId = null;
      }
    }
  };
  
  window.addEventListener('message', messageListener);
}

// 初始化消息监听器
setupMessageListener();

// 定期清理过期资源
setInterval(cleanupStaleResources, 10000); // 每10秒检查一次

// ========== 动态注入简历iframe脚本，保证Canvas hook 100%生效 ========== //
(function injectResumeFrameScriptDynamically() {
  const resumeIframeSelector = 'iframe[src*="/web/frame/c-resume/"]';
  // resume-frame-script.js的全部代码，去掉立即执行包裹
  const hookCanvasCode = `
    // 在简历iframe页面中执行的脚本
    (function() {
      console.log('iframe脚本开始执行');
      var textOperations = [];
      var isScrolling = false;
      var textSent = false;
      var currentRequestId = null;
      var scrollCompleteListener = null;
      var forceSendTimer = null;
      window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'setRequestId') {
          currentRequestId = event.data.requestId;
          console.log('收到请求ID:', currentRequestId);
        }
      });
      var originalGetContext = HTMLCanvasElement.prototype.getContext;
      HTMLCanvasElement.prototype.getContext = function() {
        var context = originalGetContext.apply(this, arguments);
        if (context && arguments[0] === '2d' && !this._resume_hooked) {
          this._resume_hooked = true;
          var originalFillText = context.fillText;
          context.fillText = function(text, x, y) {
            if (text && typeof text === 'string' && text.trim().length > 0) {
              textOperations.push({
                text: text.trim(),
                x: x,
                y: y,
                timestamp: Date.now()
              });
            }
            return originalFillText.apply(this, arguments);
          };
          if (context.strokeText) {
            var originalStrokeText = context.strokeText;
            context.strokeText = function(text, x, y) {
              if (text && typeof text === 'string' && text.trim().length > 0) {
                textOperations.push({
                  text: text.trim(),
                  x: x,
                  y: y,
                  timestamp: Date.now()
                });
              }
              return originalStrokeText.apply(this, arguments);
            };
          }
        }
        return context;
      };
      async function autoScroll() {
        if (isScrolling) return;
        isScrolling = true;
        let lastTextCount = textOperations.length;
        let maxTries = 10;
        while (maxTries-- > 0) {
          window.parent.postMessage({
            type: 'resumeScrollRequest',
            source: 'resume-frame-script',
            requestId: currentRequestId,
            data: { fromIframe: window.location.href }
          }, '*');
          await new Promise((resolve) => {
            function handleScrollComplete(event) {
              if (event.data?.type === 'resumeScrollComplete' && event.data?.source === 'parent') {
                window.removeEventListener('message', handleScrollComplete);
                resolve();
              }
            }
            window.addEventListener('message', handleScrollComplete);
          });
          await new Promise(resolve => setTimeout(resolve, 1000));
          if (textOperations.length <= lastTextCount) {
            break;
          }
          lastTextCount = textOperations.length;
        }
        isScrolling = false;
        if (!textSent) sendTextToParent();
      }
      function sendTextToParent() {
        if (textSent) return;
        if (forceSendTimer) {
          clearTimeout(forceSendTimer);
          forceSendTimer = null;
        }
        var sortedOperations = textOperations
          .sort(function(a, b) { return a.y - b.y; })
          .filter(function(op, index, self) {
            return index === self.findIndex(function(t) {
              return t.text === op.text && Math.abs(t.y - op.y) < 5;
            });
          });
        var organizedText = sortedOperations
          .map(function(op) { return op.text; })
          .join('\n')
          .replace(/(?<=.)\n(?=.)/g, '')
          .trim();
        if (!organizedText) {
          console.warn('没有提取到有效文本内容');
        }
        var message = {
          type: 'resumeTextExtracted',
          source: 'resume-frame-script',
          requestId: currentRequestId,
          data: {
            text: organizedText,
            timestamp: new Date().toISOString(),
            textLength: organizedText.length,
            operationsCount: sortedOperations.length,
            fromIframe: window.location.href,
            isComplete: true
          }
        };
        try {
          window.parent.postMessage(message, '*');
          console.log('消息已发送到父窗口');
        } catch (e) {
          console.error('发送消息失败:', e);
        }
        textSent = true;
        cleanupResources();
      }
      function cleanupResources() {
        textOperations = [];
        if (scrollCompleteListener) {
          window.removeEventListener('message', scrollCompleteListener);
          scrollCompleteListener = null;
        }
        if (forceSendTimer) {
          clearTimeout(forceSendTimer);
          forceSendTimer = null;
        }
      }
      var observer = new MutationObserver(function(mutations) {
        for (var i = 0; i < mutations.length; i++) {
          var mutation = mutations[i];
          if (mutation.type === 'childList') {
            var canvas = document.querySelector('canvas');
            if (canvas && !textSent && !isScrolling) {
              observer.disconnect();
              setTimeout(function() {
                if (!textSent) {
                  if (textOperations.length > 0) {
                    sendTextToParent();
                  } else {
                    sendTextToParent();
                  }
                }
              }, 3000);
              break;
            }
          }
        }
      });
      observer.observe(document.body, { childList: true, subtree: true });
      if (document.readyState === 'complete') {
        var canvas = document.querySelector('canvas');
        if (canvas && !textSent && !isScrolling) {
          setTimeout(function() {
            if (!textSent) {
              if (textOperations.length > 0) {
                sendTextToParent();
              } else {
                sendTextToParent();
              }
            }
          }, 2000);
        }
      }
      forceSendTimer = setTimeout(function() {
        if (!textSent) {
          sendTextToParent();
        }
      }, 10000);
    })();
  `;
  function injectToIframe(iframe) {
    try {
      if (!iframe || !iframe.contentWindow) return;
      // 防止重复注入
      if (iframe.contentWindow.__resume_hook_injected) return;
      iframe.contentWindow.__resume_hook_injected = true;
      iframe.contentWindow.eval(hookCanvasCode);
      console.log('[动态注入] 已eval注入resume-frame-script.js到iframe:', iframe.src);
      // 新增日志
      console.log('[动态注入] resume-frame-script.js 动态注入时机:', new Date().toISOString(), 'iframe.src:', iframe.src);
    } catch (e) {
      console.warn('[动态注入] 注入resume-frame-script.js失败:', e);
    }
  }
  const observer = new MutationObserver((mutations) => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.tagName === 'IFRAME' && node.src && node.src.includes('/web/frame/c-resume/')) {
          injectToIframe(node);
          node.addEventListener('load', () => injectToIframe(node));
        }
      });
    });
  });
  observer.observe(document.body, { childList: true, subtree: true });
  // 页面已存在的iframe也补一次
  document.querySelectorAll(resumeIframeSelector).forEach(injectToIframe);
})();
// ========== 动态注入简历iframe脚本，保证Canvas hook 100%生效 ========== //


